import asyncio
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.tools.connect_to_admin_tool import connect_to_admin_tool


async def main():
    print("=== Contoh <PERSON>an <PERSON>l Turn Off AI Reply ===")

    # Contoh konfigurasi
    config = {
        "configurable": {
            "chatroom_path": "/projects/WLdKug7hau0MbRzKcnqg/chat_rooms/6285763705624"
        }
    }

    print("\nMencoba menghubungkan ke admin...")
    try:
        # Memanggil tool dengan format ToolCall yang benar karena menggunakan InjectedToolCallId
        tool_call = {
            "args": {},
            "name": "connect_to_admin",
            "type": "tool_call",
            "id": "test_call_123",
        }
        result = await connect_to_admin_tool.ainvoke(tool_call, config=config)

        # Menampilkan hasil
        print("Status: Berhasil")
        print(f"Pesan: {result.update['messages'][0].content}")
        print(f"AI Reply Disabled: {result.update.get('connected_to_admin', False)}")
    except Exception as e:
        import traceback

        print(f"Error: {str(e)}")
        print("Traceback:")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
