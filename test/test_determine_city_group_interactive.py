import asyncio

import devtools
from dotenv import load_dotenv

from app.helpers.determine_city_group import adetermine_city_group

load_dotenv()


async def main():
    while True:
        user_input = input("Enter address (or 'exit' to quit): ")
        if user_input.lower() == "exit":
            break

        response = await adetermine_city_group(user_input)
        devtools.debug(response)


if __name__ == "__main__":
    asyncio.run(main())
