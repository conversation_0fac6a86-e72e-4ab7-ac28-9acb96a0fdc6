import pytest
import asyncio
from datetime import datetime, timedelta
from app.checkpoints.firestore_checkpointer import (
    FirestoreCheckpointer,
    FirestoreOperationError,
    CheckpointNotFound,
)


@pytest.fixture
def checkpointer():
    return FirestoreCheckpointer(collection_name="test_checkpoints")


@pytest.mark.asyncio
async def test_async_put_and_get_tuple(checkpointer):
    """Test async put and get_tuple methods"""
    config = {"thread_id": "test_thread_1", "state": "initial"}
    metadata = {"user_id": "user_123", "source": "test"}

    # Store checkpoint
    checkpoint_id = await checkpointer.aput(config, metadata)
    assert checkpoint_id is not None

    # Retrieve checkpoint
    get_config = {"thread_id": "test_thread_1", "checkpoint_id": checkpoint_id}
    retrieved_config, retrieved_metadata = await checkpointer.aget_tuple(get_config)

    # Verify data
    assert retrieved_config == config
    assert retrieved_metadata == metadata


@pytest.mark.asyncio
async def test_async_list_checkpoints(checkpointer):
    """Test async list method"""
    thread_id = "test_thread_2"
    config = {"thread_id": thread_id, "state": "active"}
    metadata = {"test": "data"}

    # Create multiple checkpoints
    checkpoint_ids = []
    for i in range(3):
        checkpoint_id = await checkpointer.aput(config, metadata)
        checkpoint_ids.append(checkpoint_id)
        await asyncio.sleep(0.1)  # Ensure unique timestamps

    # List checkpoints
    list_config = {"thread_id": thread_id}
    checkpoints = await checkpointer.alist(list_config)

    # Verify results
    assert len(checkpoints) == 3
    for cp in checkpoints:
        assert cp["id"] in checkpoint_ids
        assert cp["config"] == config
        assert cp["metadata"] == metadata


@pytest.mark.asyncio
async def test_async_put_writes(checkpointer):
    """Test async put_writes method"""
    # First create a checkpoint
    config = {"thread_id": "test_thread_3"}
    metadata = {}
    checkpoint_id = await checkpointer.aput(config, metadata)

    # Store writes
    writes = [{"operation": "create", "data": {"key": "value"}}]
    await checkpointer.aput_writes(checkpoint_id, writes)

    # No direct way to verify writes without get_writes method
    # But if no exception, we assume success


def test_sync_put_and_get_tuple(checkpointer):
    """Test sync put and get_tuple methods"""
    config = {"thread_id": "test_thread_4", "state": "completed"}
    metadata = {"status": "success"}

    # Store checkpoint
    checkpoint_id = checkpointer.put(config, metadata)
    assert checkpoint_id is not None

    # Retrieve checkpoint
    get_config = {"thread_id": "test_thread_4", "checkpoint_id": checkpoint_id}
    retrieved_config, retrieved_metadata = checkpointer.get_tuple(get_config)

    # Verify data
    assert retrieved_config == config
    assert retrieved_metadata == metadata


@pytest.mark.asyncio
async def test_checkpoint_not_found(checkpointer):
    """Test handling of non-existent checkpoint"""
    get_config = {"thread_id": "non_existent_thread", "checkpoint_id": "invalid_id_123"}

    with pytest.raises(CheckpointNotFound):
        await checkpointer.aget_tuple(get_config)


@pytest.mark.asyncio
async def test_thread_id_mismatch(checkpointer):
    """Test thread ID verification"""
    # Create checkpoint
    config = {"thread_id": "correct_thread"}
    metadata = {}
    checkpoint_id = await checkpointer.aput(config, metadata)

    # Try to retrieve with wrong thread ID
    get_config = {"thread_id": "wrong_thread", "checkpoint_id": checkpoint_id}

    with pytest.raises(ValueError, match="Thread ID mismatch"):
        await checkpointer.aget_tuple(get_config)


@pytest.mark.asyncio
async def test_list_with_filters(checkpointer):
    """Test list method with time filters"""
    thread_id = "test_thread_filters"
    config = {"thread_id": thread_id}
    metadata = {}

    # Create checkpoints at different times
    now = datetime.utcnow()

    # Old checkpoint (2 hours ago)
    old_checkpoint_id = await checkpointer.aput(config, metadata)

    # Recent checkpoints
    await asyncio.sleep(1)
    checkpoint_id1 = await checkpointer.aput(config, metadata)
    checkpoint_id2 = await checkpointer.aput(config, metadata)

    # List recent checkpoints (last 1 hour)
    list_config = {"thread_id": thread_id}
    recent_checkpoints = await checkpointer.alist(
        list_config, created_after=now - timedelta(minutes=30)
    )

    # Verify results
    recent_ids = [cp["id"] for cp in recent_checkpoints]
    assert checkpoint_id1 in recent_ids
    assert checkpoint_id2 in recent_ids
    assert old_checkpoint_id not in recent_ids
