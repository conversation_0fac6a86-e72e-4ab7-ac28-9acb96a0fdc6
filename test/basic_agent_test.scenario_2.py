import asyncio

from dotenv import load_dotenv
from langchain_core.callbacks import UsageMetadataCallbackHandler
from langchain_core.messages import HumanMessage

from app.agents.whatsapp_basic_agent_chat import WhatsappBasicAgentChat
from app.llm.gemini import gemini

load_dotenv()

agent = WhatsappBasicAgentChat()

callback = UsageMetadataCallbackHandler()


async def main():
    messages = []
    while True:
        user_input = input("User: ")
        if user_input == "exit":
            break
        messages.append(HumanMessage(content=user_input))
        m = await chat(messages)
        messages = m["messages"]
        print(callback.usage_metadata)
        print(m["final_answer"])


async def chat(messages: list):
    response = await agent.runnable.ainvoke(
        {
            "messages": [*messages],
            "real_name": "Budi",
            "city_group": "bekasi",
        },
        {
            "configurable": {
                "model": gemini,
                "organization": "amarta",
            },
            "callbacks": [callback],
            "recursion_limit": 25,
        },
    )

    return response


asyncio.run(main())
