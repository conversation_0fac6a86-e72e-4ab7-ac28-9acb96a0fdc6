import asyncio
import unittest
from unittest.mock import patch, MagicMock, AsyncMock

from langchain_core.tools import ToolException

from app.tools.get_variants_tool import (
    get_variants_tool,
    _aget_variants_tool,
    _filter_variants,
    _format_variant_output,
    _fetch_specifications,
    VariantSchema,
)


class TestGetVariantsTool(unittest.TestCase):
    def setUp(self):
        """Set up test data"""
        self.sample_variants_data = [
            {
                "variant_code": "H1B02N41L1A/T",
                "variant_name": "BeAT Deluxe CBS",
                "model_name": "BeAT",
                "price": 18500000,
                "url_image": "https://example.com/image1.jpg",
                "url_vehicle_specification": "https://example.com/specs/beat_deluxe.json",
            },
            {
                "variant_code": "H1B02N41L2A/T",
                "variant_name": "BeAT Street CBS",
                "model_name": "BeAT",
                "price": 17800000,
                "url_image": "https://example.com/image2.jpg",
                "url_vehicle_specification": "https://example.com/specs/beat_street.json",
            },
        ]

        self.sample_specifications = {
            "H1B02N41L1A/T": {
                "engine": "110cc, SOHC, 4-Stroke",
                "max_power": "9.0 PS @ 7,500 rpm",
                "max_torque": "9.1 Nm @ 5,500 rpm",
                "fuel_system": "PGM-FI",
                "fuel_capacity": "4.2 liter",
                "dimensions": "1,877 x 677 x 1,061 mm",
                "dry_weight": "89 kg"
            }
        }

        self.sample_api_response = {
            "data": self.sample_variants_data,
            "status": "success",
        }

        self.expected_csv_output = (
            "variant_code,variant_name,model_name,price,product_link\n"
            "H1B02N41L1A/T,BeAT Deluxe CBS,BeAT,18500000,https://amartahonda.com/baru/bandung/H1B02N41L1AT\n"
            "H1B02N41L2A/T,BeAT Street CBS,BeAT,17800000,https://amartahonda.com/baru/bandung/H1B02N41L2AT"
        )

    def test_variant_schema_validation(self):
        """Test VariantSchema validation"""
        # Valid schema with required fields
        valid_schema = VariantSchema(model_names=["BeAT"], city_group="bandung")
        self.assertEqual(valid_schema.model_names, ["BeAT"])
        self.assertEqual(valid_schema.city_group, "bandung")
        self.assertEqual(valid_schema.include_specs, False)  # Default value

        # With include_specs=True
        schema_with_specs = VariantSchema(model_names=["PCX 160"], city_group="jakarta", include_specs=True)
        self.assertEqual(schema_with_specs.include_specs, True)
        self.assertEqual(schema_with_specs.city_group, "jakarta")
        self.assertEqual(schema_with_specs.model_names, ["PCX 160"])

    def test_filter_variants_valid_data(self):
        """Test _filter_variants with valid data"""
        result = _filter_variants(self.sample_variants_data)
        self.assertEqual(len(result), 2)
        self.assertEqual(result, self.sample_variants_data)

    def test_filter_variants_empty_data(self):
        """Test _filter_variants with empty data"""
        result = _filter_variants([])
        self.assertEqual(result, [])

        result = _filter_variants(None)
        self.assertEqual(result, [])

    def test_filter_variants_invalid_data(self):
        """Test _filter_variants with invalid data structure"""
        invalid_variants = [
            {"variant_code": "H1B02N41L1A/T"},  # Missing required fields
            {
                "variant_code": "H1B02N41L2A/T",
                "variant_name": "BeAT Street CBS",
                "model_name": "BeAT",
                "price": 17800000,
            },  # Valid variant
        ]

        result = _filter_variants(invalid_variants)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["variant_code"], "H1B02N41L2A/T")

    def test_format_variant_output_valid_data(self):
        """Test _format_variant_output with valid data"""
        result = _format_variant_output(self.sample_variants_data, "bandung", "amarta")
        
        # Check if all expected information is present
        self.assertIn("BeAT Deluxe CBS", result)
        self.assertIn("Rp 18.500.000", result)
        self.assertIn("H1B02N41L1A/T", result)
        self.assertIn("https://amartahonda.com/baru/bandung/H1B02N41L1AT", result)

    def test_format_variant_output_empty_data(self):
        """Test _format_variant_output with empty data"""
        result = _format_variant_output([], "bandung", "amarta")
        expected = "Tidak ada varian yang tersedia untuk model ini."
        self.assertEqual(result, expected)

    def test_format_variant_output_with_commas(self):
        """Test _format_variant_output with data containing commas"""
        variants_with_commas = [
            {
                "variant_code": "H1B02N41L1A/T",
                "variant_name": "BeAT Deluxe, CBS",
                "model_name": "BeAT, Special",
                "price": 18500000,
                "ai_notes": "Motor matic terbaik",
                "url_vehicle_specification": "https://example.com/specs.json"
            }
        ]

        result = _format_variant_output(variants_with_commas, "bandung", "amarta")
        self.assertIn("BeAT Deluxe, CBS", result)
        self.assertIn("BeAT, Special", result)

    def test_format_variant_output_url_generation(self):
        """Test URL generation in _format_variant_output"""
        result = _format_variant_output(self.sample_variants_data, "jakarta", "amarta")
        self.assertIn("https://amartahonda.com/baru/jakarta/H1B02N41L1AT", result)
        self.assertIn("https://amartahonda.com/baru/jakarta/H1B02N41L2AT", result)

    def test_format_variant_output_with_specs_true_no_specifications(self):
        """Test _format_variant_output with include_specs=True but no specifications provided"""
        result = _format_variant_output(self.sample_variants_data, "bandung", "amarta", include_specs=True)
        
        # Should include specs URL when specifications not available
        self.assertIn("URL Spesifikasi: https://example.com/specs/beat_deluxe.json", result)
        self.assertIn("URL Spesifikasi: https://example.com/specs/beat_street.json", result)

    def test_format_variant_output_with_specs_true_with_specifications(self):
        """Test _format_variant_output with include_specs=True and specifications provided"""
        result = _format_variant_output(
            self.sample_variants_data, 
            "bandung", 
            "amarta",
            include_specs=True, 
            specifications=self.sample_specifications
        )
        
        # Should include detailed specifications
        self.assertIn("Spesifikasi Teknis:", result)
        self.assertIn("Engine: 110cc, SOHC, 4-Stroke", result)
        self.assertIn("Max Power: 9.0 PS @ 7,500 rpm", result)
        self.assertIn("Max Torque: 9.1 Nm @ 5,500 rpm", result)

    def test_format_variant_output_with_specs_false(self):
        """Test _format_variant_output with include_specs=False (default)"""
        result = _format_variant_output(self.sample_variants_data, "bandung", "amarta", include_specs=False)
        
        # Should not include any specifications
        self.assertNotIn("URL Spesifikasi:", result)
        self.assertNotIn("Spesifikasi Teknis:", result)
        self.assertNotIn("Engine:", result)


class TestFetchSpecifications(unittest.TestCase):
    """Test cases for _fetch_specifications function"""
    
    def setUp(self):
        self.sample_api_response = {
            "engine": "110cc, 4-tak, SOHC",
            "max_power": "9.0 PS @ 7500 rpm",
            "fuel_capacity": "4.2 liter"
        }

    @patch('aiohttp.ClientSession.get')
    async def test_fetch_specifications_success(self, mock_get):
        """Test successful specification fetching"""
        # Mock response
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "engine": "110cc, SOHC, 4-Stroke",
            "max_power": "9.0 PS @ 7,500 rpm"
        }
        mock_response.__aenter__.return_value = mock_response
        mock_get.return_value = mock_response

        result = await _fetch_specifications("https://example.com/specs.json")
        
        self.assertEqual(result["engine"], "110cc, SOHC, 4-Stroke")
        self.assertEqual(result["max_power"], "9.0 PS @ 7,500 rpm")

    @patch('aiohttp.ClientSession.get')
    async def test_fetch_specifications_http_error(self, mock_get):
        """Test HTTP error handling"""
        mock_get.side_effect = Exception("HTTP Error")

        result = await _fetch_specifications("https://example.com/specs.json")
        
        self.assertIsNone(result)

    @patch('aiohttp.ClientSession.get')
    async def test_fetch_specifications_json_decode_error(self, mock_get):
        """Test JSON decode error handling"""
        mock_response = AsyncMock()
        mock_response.json.side_effect = Exception("JSON decode error")
        mock_response.__aenter__.return_value = mock_response
        mock_get.return_value = mock_response

        result = await _fetch_specifications("https://example.com/specs.json")
        
        self.assertIsNone(result)

    # Removed _get_variants_tool test since the function doesn't exist in the main file

    @patch("app.tools.get_variants_tool.CatalogueService")
    async def test_aget_variants_tool_success(self, mock_variant_service):
        """Test _aget_variants_tool with successful API response"""
        # Setup mock
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance

        # Create an async mock for aget_variants
        async def mock_aget_variants(*args, **kwargs):
            return self.sample_api_response

        mock_service_instance.aget_variants = mock_aget_variants

        # Call function
        result = await _aget_variants_tool(["BeAT"], "bandung")

        # Assertions
        self.assertEqual(result, self.expected_csv_output)

    @patch("app.tools.get_variants_tool._fetch_specifications")
    @patch("app.tools.get_variants_tool.CatalogueService")
    async def test_aget_variants_tool_with_specs_true(self, mock_variant_service, mock_fetch_specs):
        """Test async variant retrieval with include_specs=True"""
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance
        
        async def mock_aget_variants(*args, **kwargs):
            return self.sample_api_response
        
        mock_service_instance.aget_variants = mock_aget_variants
        mock_fetch_specs.return_value = {
            "engine": "110cc, SOHC, 4-Stroke",
            "max_power": "9.0 PS @ 7,500 rpm"
        }

        result = await _aget_variants_tool(["BeAT"], "bandung", include_specs=True)

        self.assertIn("BeAT Deluxe CBS", result)
        self.assertIn("Spesifikasi Teknis:", result)
        self.assertIn("Engine: 110cc, SOHC, 4-Stroke", result)
        
        # Should call fetch_specifications for unique URLs
        self.assertEqual(mock_fetch_specs.call_count, 2)  # Two unique spec URLs

    @patch("app.tools.get_variants_tool.CatalogueService")
    async def test_aget_variants_tool_with_specs_false(self, mock_variant_service):
        """Test async variant retrieval with include_specs=False (default)"""
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance
        
        async def mock_aget_variants(*args, **kwargs):
            return self.sample_api_response
        
        mock_service_instance.aget_variants = mock_aget_variants

        result = await _aget_variants_tool(["BeAT"], "bandung", include_specs=False)

        self.assertIn("BeAT Deluxe CBS", result)
        self.assertNotIn("Spesifikasi", result)

    # Removed _get_variants_tool empty model name test since the function doesn't exist

    async def test_aget_variants_tool_empty_model_name(self):
        """Test _aget_variants_tool with empty model name"""
        with self.assertRaises(ToolException) as context:
            await _aget_variants_tool([], "bandung")

        self.assertIn("model_names tidak boleh kosong", str(context.exception))

    # Removed _get_variants_tool empty city group test since the function doesn't exist

    # Removed _get_variants_tool no variants data test since the function doesn't exist

    # Removed _get_variants_tool empty API response test since the function doesn't exist

    # Removed _get_variants_tool API exception test since the function doesn't exist

    # Removed _get_variants_tool input normalization test since the function doesn't exist

    async def test_structured_tool_invocation(self):
        """Test StructuredTool invocation"""
        with patch("app.tools.get_variants_tool._aget_variants_tool") as mock_aget_variants:
            mock_aget_variants.return_value = "mocked result"

            # Invoke the tool
            result = await get_variants_tool.ainvoke(
                {"model_names": ["BeAT"], "city_group": "bandung"}
            )

            # Verify the mock was called with correct parameters
            mock_aget_variants.assert_called_once_with(["BeAT"], "bandung", False)
            self.assertEqual(result, "mocked result")

    async def test_structured_tool_invocation_with_specs(self):
        """Test StructuredTool invocation with include_specs=True"""
        with patch("app.tools.get_variants_tool._aget_variants_tool") as mock_aget_variants:
            mock_aget_variants.return_value = "Mocked result with specs"

            result = await get_variants_tool.ainvoke({
                "model_names": ["BeAT"], 
                "city_group": "bandung", 
                "include_specs": True
            })

            self.assertEqual(result, "Mocked result with specs")
            mock_aget_variants.assert_called_once_with(["BeAT"], "bandung", True)

    # Removed test_structured_tool_invoke_success because StructuredTool doesn't support sync invocation

    @patch("app.tools.get_variants_tool.CatalogueService")
    async def test_structured_tool_ainvoke_success(self, mock_variant_service):
        """Test the actual StructuredTool ainvoke method"""
        # Setup mock
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance

        # Create an async mock for aget_variants
        async def mock_aget_variants(*args, **kwargs):
            return self.sample_api_response

        mock_service_instance.aget_variants = mock_aget_variants

        # Call the tool
        result = await get_variants_tool.ainvoke(
            {"model_names": ["BeAT"], "city_group": "bandung"}
        )

        # Assertions
        self.assertEqual(result, self.expected_csv_output)


def run_async_test(coro):
    """Helper function to run async tests"""
    return asyncio.run(coro)


if __name__ == "__main__":
    # Run the tests
    test = TestGetVariantsTool()

    # Run sync tests
    test.setUp()
    test.test_variant_schema_validation()
    test.test_filter_variants_valid_data()
    test.test_filter_variants_empty_data()
    test.test_filter_variants_invalid_data()
    test.test_format_variant_output_valid_data()
    test.test_format_variant_output_empty_data()
    test.test_format_variant_output_with_commas()
    test.test_format_variant_output_url_generation()

    # Run async tests
    run_async_test(test.test_aget_variants_tool_success())
    run_async_test(test.test_aget_variants_tool_empty_model_name())
    run_async_test(test.test_structured_tool_ainvoke_success())

    print("All tests passed!")
