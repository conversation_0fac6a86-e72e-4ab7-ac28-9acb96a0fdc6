import asyncio
import pytest
from langchain_core.runnables import RunnableConfig

from app.tools.get_vehicle_promo_tool import (
    get_vehicle_promo_tool,
    _aget_vehicle_promo_tool,
)


@pytest.mark.asyncio
async def test_get_vehicle_promo_tool_basic():
    """
    Test dasar untuk tool get_vehicle_promo dengan model saja.
    """
    try:
        # Test dengan model saja
        config = RunnableConfig(configurable={"organization": "amarta"})
        result = await _aget_vehicle_promo_tool(model_name="scoopy", config=config)

        print("=== Test dengan model saja ===")
        print(result)
        print("\n" + "=" * 50 + "\n")

        assert isinstance(result, str)
        assert "PROMO UNTUK SCOOPY" in result.upper()

    except Exception as e:
        print(f"Error dalam test basic: {e}")
        # Tidak fail test jika API tidak tersedia
        pass


@pytest.mark.asyncio
async def test_get_vehicle_promo_tool_with_variant():
    """
    Test tool get_vehicle_promo dengan model dan variant code.
    """
    try:
        # Test dengan model dan variant code
        config = RunnableConfig(configurable={"organization": "amarta"})
        result = await _aget_vehicle_promo_tool(
            model_name="scoopy", variant_code="F1C02N46L2A/T", config=config
        )

        print("=== Test dengan model dan variant ===")
        print(result)
        print("\n" + "=" * 50 + "\n")

        assert isinstance(result, str)
        assert "PROMO UNTUK SCOOPY" in result.upper()
        assert "F1C02N46L2A/T" in result

    except Exception as e:
        print(f"Error dalam test with variant: {e}")
        # Tidak fail test jika API tidak tersedia
        pass


@pytest.mark.asyncio
async def test_get_vehicle_promo_tool_structured():
    """
    Test menggunakan StructuredTool interface.
    """
    try:
        # Test menggunakan StructuredTool
        config = RunnableConfig(configurable={"organization": "amarta"})

        result = await get_vehicle_promo_tool.ainvoke(
            {"model_name": "beat", "variant_code": None}, config=config
        )

        print("=== Test StructuredTool interface ===")
        print(result)
        print("\n" + "=" * 50 + "\n")

        assert isinstance(result, str)

    except Exception as e:
        print(f"Error dalam test structured: {e}")
        # Tidak fail test jika API tidak tersedia
        pass


@pytest.mark.asyncio
async def test_get_vehicle_promo_tool_invalid_input():
    """
    Test dengan input yang tidak valid.
    """
    try:
        # Test dengan model kosong
        config = RunnableConfig(configurable={"organization": "amarta"})

        result = await _aget_vehicle_promo_tool(model_name="", config=config)

        # Seharusnya tidak sampai sini karena akan raise exception
        assert False, "Seharusnya raise ToolException untuk model kosong"

    except Exception as e:
        print(f"=== Test input tidak valid (expected error) ===")
        print(f"Error: {e}")
        print("\n" + "=" * 50 + "\n")

        # Expected behavior - should raise exception
        assert "tidak boleh kosong" in str(e)


# Fungsi main() dan blok if __name__ == "__main__" tidak diperlukan lagi karena kita menggunakan pytest
# Untuk menjalankan test secara manual, gunakan: python -m pytest test/test_get_vehicle_promo_tool.py -v
