import asyncio

import devtools
from dotenv import load_dotenv
from langchain_core.callbacks import UsageMetadataCallbackHandler
from langchain_core.load import load
from langchain_core.messages import HumanMessage

from app.agents.whatsapp_basic_agent_chat import WhatsappBasicAgentChat
from app.llm.gemini import gemini
from app.llm.groq import groq
from app.services.firebase_service import afirestore_client

load_dotenv()

agent = WhatsappBasicAgentChat()


callback = UsageMetadataCallbackHandler()


async def fetchMessage():
    """
    Fetch messages from firestore
    Collection = /projects/WLdKug7hau0MbRzKcnqg/chat_rooms/628122241034/chats
    Order by 'order' 'asc'
    """

    collection_path = (
        "projects/WLdKug7hau0MbRzKcnqg/chat_rooms/628122241034/ai_agent_chats"
    )

    db = afirestore_client()
    docs = (
        db.collection(collection_path).order_by("order", direction="ASCENDING").stream()
    )
    messages = []
    async for doc in docs:
        data = doc.to_dict()
        langchain_message = load(data["langchain_message"])
        messages.append(langchain_message)

    return messages


async def chat(messages: list):
    response = await agent.runnable.ainvoke(
        {"messages": [*messages]},
        {
            "configurable": {
                "model": gemini,
            },
            "callbacks": [callback],
            "recursion_limit": 50,
        },
    )

    return response


async def main():
    messages = await fetchMessage()
    messages = messages[:-3]
    response = await chat(messages)
    print(response["final_answer"])


asyncio.run(main())
