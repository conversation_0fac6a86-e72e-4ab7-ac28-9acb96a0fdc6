import asyncio
import unittest
from unittest.mock import patch, MagicMock

from app.services.turn_off_ai_reply_service import (
    turn_off_ai_reply,
    turn_off_ai_reply_sync,
)


class TestTurnOffAiReplyService(unittest.TestCase):
    @patch("app.services.turn_off_ai_reply_service.ideal_api_client")
    async def test_turn_off_ai_reply_success(self, mock_api_client):
        # Setup mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "success"}
        mock_response.raise_for_status.return_value = None

        # Setup mock client
        mock_api_client.post.return_value = mock_response

        # Test data
        chat_room_path = "/test/chat_room/path"

        # Call the service
        result = await turn_off_ai_reply(chat_room_path)

        # Assertions
        self.assertEqual(result, {"status": "success"})

        # Verify API was called with correct parameters
        mock_api_client.post.assert_called_once_with(
            "/public/toggle-agent-ai-reply",
            json={"chatRoomPath": chat_room_path, "agentAiReply": False},
        )

    @patch("app.services.turn_off_ai_reply_service.ideal_api_client")
    async def test_turn_off_ai_reply_error(self, mock_api_client):
        # Setup mock to raise exception
        mock_api_client.post.side_effect = Exception("API Error")

        # Test data
        chat_room_path = "/test/chat_room/path"

        # Call the service
        result = await turn_off_ai_reply(chat_room_path)

        # Assertions
        self.assertIsNone(result)

    @patch("app.services.turn_off_ai_reply_service.ideal_sync_api_client")
    def test_turn_off_ai_reply_sync_success(self, mock_api_client):
        # Setup mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "success"}
        mock_response.raise_for_status.return_value = None

        # Setup mock client
        mock_api_client.post.return_value = mock_response

        # Test data
        chat_room_path = "/test/chat_room/path"

        # Call the service
        result = turn_off_ai_reply_sync(chat_room_path)

        # Assertions
        self.assertEqual(result, {"status": "success"})

        # Verify API was called with correct parameters
        mock_api_client.post.assert_called_once_with(
            "/public/toggle-agent-ai-reply",
            json={"chatRoomPath": chat_room_path, "agentAiReply": False},
        )

    @patch("app.services.turn_off_ai_reply_service.ideal_sync_api_client")
    def test_turn_off_ai_reply_sync_error(self, mock_api_client):
        # Setup mock to raise exception
        mock_api_client.post.side_effect = Exception("API Error")

        # Test data
        chat_room_path = "/test/chat_room/path"

        # Call the service
        result = turn_off_ai_reply_sync(chat_room_path)

        # Assertions
        self.assertIsNone(result)


def run_async_test(coro):
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(coro)


if __name__ == "__main__":
    # Run the async tests
    test = TestTurnOffAiReplyService()
    run_async_test(test.test_turn_off_ai_reply_success())
    run_async_test(test.test_turn_off_ai_reply_error())

    # Run the sync tests
    test.test_turn_off_ai_reply_sync_success()
    test.test_turn_off_ai_reply_sync_error()

    print("All tests passed!")
