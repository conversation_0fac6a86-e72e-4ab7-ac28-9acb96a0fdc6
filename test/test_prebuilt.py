import asyncio

from langchain_core.messages import HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolCallId, StructuredTool
from langgraph.constants import START, END
from langgraph.graph import MessagesState, StateGraph
from langgraph.prebuilt import (
    create_react_agent,
    InjectedState,
    ToolNode,
    tools_condition,
)
from langgraph.prebuilt.chat_agent_executor import AgentState
from langgraph.types import Command
from pydantic import BaseModel, Field
from typing_extensions import Annotated

from app.llm.gemini import gemini
from app.llm.groq import groq
from app.tools.save_user_city_domicile_tool import _asave_user_city_domicile


class MyState(AgentState):
    name: str | None
    city_group: str | None


class Args(BaseModel):
    new_name: str = Field(description="The new name to be updated for the user.")
    state: Annotated[dict, InjectedState]
    tool_call_id: Annotated[str, InjectedToolCallId]


async def aupdate_my_name(
    new_name: str,
    state: InjectedState,
    tool_call_id: InjectedToolCallId,
    config: RunnableConfig,
):
    print(state)
    print(tool_call_id)
    print(config)
    tool_message = ToolMessage(
        content=f"Name updated to {new_name}",
        tool_call_id=tool_call_id,
    )
    return f"Name updated to {new_name} and tool message is {tool_message}"


tool_update_my_name = StructuredTool.from_function(
    name="update_my_name",
    args_schema=Args,
    coroutine=aupdate_my_name,
    description="Update my name to the new name provided.",
    return_direct=False,
)

tools = [tool_update_my_name]
tool_node = ToolNode(tools=tools)

llm = groq.bind_tools(tools)


async def chat(state: MyState, config: RunnableConfig):
    response = await llm.ainvoke(state["messages"])
    return {"messages": [response]}


state_graph = StateGraph(MyState)


state_graph.add_node("chat", chat)
state_graph.add_node("tools", tool_node)

state_graph.add_edge(START, "chat")
state_graph.add_conditional_edges(
    "chat",
    tools_condition,
)

agent = state_graph.compile()

if __name__ == "__main__":

    async def main():
        response = await agent.ainvoke(
            {
                "messages": [
                    # HumanMessage("Perbarui domisili saya di bandung")
                    HumanMessage("Perbarui nama saya menjadi John")
                ]
            }
        )
        print(response)

    asyncio.run(main())
