import asyncio

from dotenv import load_dotenv
from langchain_core.callbacks import UsageMetadataCallbackHandler
from langchain_core.messages import HumanMessage

from app.agents.whatsapp_basic_agent_chat import WhatsappBasicAgentChat
from app.helpers.firestore_ai_agent_messages import load_chat_room_messages
from app.llm.gemini import gemini
from app.llm.groq import groq

load_dotenv()

agent = WhatsappBasicAgentChat()


callback = UsageMetadataCallbackHandler()
chat_room_path = "projects/WLdKug7hau0MbRzKcnqg/chat_rooms/6285763705624"

# init firebase
from app.services.firebase_service import initialize_firebase

initialize_firebase(key_path="../app/keys/firebase/ideal-trimitra-2620981b291f.json")


async def main():
    messages = await load_chat_room_messages(chat_room_path)

    for message in messages:
        # content and name
        print(message.pretty_repr())

    while True:
        user_input = input("User: ")
        if user_input == "exit":
            break
        messages.append(HumanMessage(content=user_input))
        m = await chat(messages)
        messages = m["messages"]
        print(messages[-1].content)


async def chat(messages: list):
    response = await agent.runnable.ainvoke(
        {"messages": [*messages]},
        {
            "configurable": {
                "model": gemini,
                "chatroom_path": chat_room_path,
            },
            "callbacks": [callback],
        },
    )

    return response


asyncio.run(main())
