import asyncio
import unittest
from unittest.mock import patch, MagicMock

from app.tools.connect_to_admin_tool import connect_to_admin_tool


class TestTurnOffAiReplyTool(unittest.TestCase):
    @patch("app.services.turn_off_ai_reply_service.turn_off_ai_reply")
    async def test_aturn_off_ai_reply_success(self, mock_turn_off_ai_reply):
        # Setup mock
        mock_turn_off_ai_reply.return_value = {"status": "success"}

        # Test data
        config = {"configurable": {"chatroom_path": "/test/chat_room/path"}}

        # Call the tool
        result = await connect_to_admin_tool.ainvoke({}, config=config)

        # Assertions
        self.assertIsNotNone(result)
        self.assertIn("update", result)
        self.assertIn("messages", result["update"])
        self.assertIn("connected_to_admin", result["update"])
        self.assertTrue(result["update"]["connected_to_admin"])

        # Verify the service was called with correct parameters
        mock_turn_off_ai_reply.assert_called_once_with(
            chat_room_path="/test/chat_room/path",
            conversation_resume="User requested connection to admin",
        )

    @patch("app.services.turn_off_ai_reply_service.turn_off_ai_reply")
    async def test_aturn_off_ai_reply_failure(self, mock_turn_off_ai_reply):
        # Setup mock to simulate failure
        mock_turn_off_ai_reply.return_value = None

        # Test data
        config = {"configurable": {"chatroom_path": "/test/chat_room/path"}}

        # Call the tool
        result = await connect_to_admin_tool.ainvoke({}, config=config)

        # Assertions
        self.assertIsNotNone(result)
        self.assertIn("update", result)
        self.assertIn("messages", result["update"])
        self.assertNotIn("connected_to_admin", result["update"])

        # Verify the service was called
        mock_turn_off_ai_reply.assert_called_once()


def run_async_test(coro):
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(coro)


if __name__ == "__main__":
    # Run the async tests
    test = TestTurnOffAiReplyTool()
    run_async_test(test.test_aturn_off_ai_reply_success())
    run_async_test(test.test_aturn_off_ai_reply_failure())

    print("All tests passed!")
