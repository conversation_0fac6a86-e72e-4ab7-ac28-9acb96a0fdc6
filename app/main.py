from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from app.routers import chat_ideal_router, summary_router, trimitra_chat_router
from app.schemas.base_response import ErrorResponse
from app.services.firebase_service import initialize_firebase


@asynccontextmanager
async def startup_event(app: FastAPI):
    initialize_firebase()
    yield


app = FastAPI(lifespan=startup_event)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    errors = []
    for error in exc.errors():
        field = ".".join(map(str, error.get("loc", [])))
        message = error.get("msg")
        input_value = error.get("input")
        errors.append({"field": field, "message": message, "input_value": input_value})

    error_message = "Request validation error"
    return JSONResponse(
        status_code=422,
        content={
            "status_code": 422,
            "success": False,
            "message": error_message,
            "errors": errors,
        },
    )


@app.exception_handler(ErrorResponse)
async def generic_exception_handler(request: Request, exc: ErrorResponse):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status_code": exc.status_code,
            "success": False,
            "message": exc.message,
            "errors": exc.errors,
        },
    )


app.include_router(chat_ideal_router.router)
app.include_router(summary_router.router)
app.include_router(trimitra_chat_router.router)


@app.get("/")
async def root():
    return {"version": "0.0.1"}
