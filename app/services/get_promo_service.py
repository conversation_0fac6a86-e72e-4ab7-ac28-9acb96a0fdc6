import asyncio
from typing import Dict, Any, Optional

from app.services.api_client import amarta_api_client


class GetPromoService:
    """Service untuk mengambil data promo dari API Amarta."""

    async def aget_promo_from_ad_source_id(self, source_id: str) -> Dict[str, Any]:
        """
        Mengambil data promo secara asynchronous berdasarkan source ID.

        Args:
            source_id (str): ID source untuk mengambil data promo

        Returns:
            Dict[str, Any]: Response JSON dari API yang berisi data promo

        Raises:
            httpx.HTTPStatusError: Jika terjadi error HTTP
        """
        if not source_id:
            raise ValueError("source_id tidak boleh kosong")

        endpoint = "/deal/all/sourceid"
        params = {"source_id": source_id}
        response = await amarta_api_client.get(endpoint, params=params)
        response.raise_for_status()
        return response.json()

    async def aget_promo_by_model_and_variant(
        self, organization: str, model_name: str, variant_code: str, ai: bool = True
    ) -> Dict[str, Any]:
        """
        Mengambil data promo berdasarkan model dan variant code.

        Args:
            organization (str): Organisasi kendaraan (amarta, vinfast, dll.)
            model_name (str): Nama model kendaraan
            variant_code (str): Kode varian kendaraan
            ai (bool): Flag untuk AI, default True

        Returns:
            Dict[str, Any]: Response JSON dari API
        """
        endpoint = "/deal/{organization}".format(organization=organization)
        params = {"ai": ai, "model": model_name, "variant_code": variant_code}
        response = await amarta_api_client.get(endpoint, params=params)
        response.raise_for_status()
        return response.json()

    async def aget_promo_by_code(self, code: str) -> Dict[str, Any]:
        """
        Mengambil data promo berdasarkan kode promo.

        Args:
            code (str): Kode promo

        Returns:
            Dict[str, Any]: Response JSON dari API
        """
        endpoint = "/deal/all/code"
        params = {"code": code}
        response = await amarta_api_client.get(endpoint, params=params)
        response.raise_for_status()
        return response.json()


if __name__ == "__main__":

    async def main():
        service = GetPromoService()

        # Example usage
        try:
            print("--- Mengambil promo berdasarkan source ID ---")
            promo_data_source = await service.aget_promo_from_ad_source_id(
                "6730637160071"
            )
            print(f"Caption: {promo_data_source.get('data', {}).get('caption')}")

            print("\n--- Mengambil promo berdasarkan model dan varian ---")
            promo_data_model = await service.aget_promo_by_model_and_variant(
                organization="amarta", model_name="scoopy", variant_code="F1C02N46L2A/T"
            )
            print(f"Total Data: {len(promo_data_model.get('data', []))}")

            print("\n--- Mengambil promo berdasarkan kode ---")
            promo_data_code = await service.aget_promo_by_code("SCOOPYDP800CRB")
            print(f"Caption: {promo_data_code.get('data', {}).get('caption')}")

        except Exception as e:
            print(f"Error: {e}")

    asyncio.run(main())
