import asyncio

from app.services.api_client import amarta_api_client


class CityGroupService:
    def __init__(self, org: str = "amarta"):
        self.url = "/area/{organization}/city_group".format(organization=org)

    async def aget_city_groups(self):
        response = await amarta_api_client.get(self.url)
        response.raise_for_status()
        return response.json()


if __name__ == "__main__":

    async def main():
        service = CityGroupService(org="vinfast")
        city_groups = await service.aget_city_groups()
        print(city_groups)

    asyncio.run(main())
