import httpx

amarta_api_config = {
    "base_url": "https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1",
    "headers": {"x-api-key": "kHhfCAPtSu34ObnDWjI9S4K9nA086VBg2eXchdQW"},
}

amarta_api_client = httpx.AsyncClient(**amarta_api_config)

trimitra_catalog_api_config = {
    "base_url": "https://trimitra-catalog.s3.ap-southeast-1.amazonaws.com",
}

trimitra_catalog_api_client = httpx.AsyncClient(**trimitra_catalog_api_config)

b2b_api_config = {
    "base_url": "https://asia-southeast2-amh-b2b.cloudfunctions.net/api-b2b",
}

b2b_api_client = httpx.AsyncClient(**b2b_api_config)

ideal_api_config = {
    "base_url": "https://asia-southeast2-ideal-trimitra.cloudfunctions.net/ideal-backend",
}

ideal_api_client = httpx.AsyncClient(**ideal_api_config)
