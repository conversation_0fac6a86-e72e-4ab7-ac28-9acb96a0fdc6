import asyncio
from typing import Dict, Any

from app.services.api_client import amarta_api_client


class DealerService:
    """Service untuk mengambil data dealer dari <PERSON>."""

    async def aget_dealer_data(self, organization: str) -> Dict[str, Any]:
        """
        Mengambil data dealer secara asynchronous berdasarkan organization.

        Args:
            organization (str): Nama organisasi (vinfast atau amarta)

        Returns:
            Dict[str, Any]: Response JSON dari API yang berisi data dealer

        Raises:
            ValueError: Jika organization tidak valid
            httpx.HTTPStatusError: Jika terjadi error HTTP
        """
        # Validasi input organization
        valid_organizations = ["vinfast", "amarta"]
        if organization not in valid_organizations:
            raise ValueError(
                f"Organization '{organization}' tidak valid. "
                f"Harus salah satu dari: {', '.join(valid_organizations)}"
            )

        endpoint = f"/project/{organization}"
        response = await amarta_api_client.get(endpoint)
        response.raise_for_status()
        return response.json()


if __name__ == "__main__":

    async def main():
        service = DealerService()

        # Test untuk organization yang valid
        valid_organizations = ["vinfast", "amarta"]

        for org in valid_organizations:
            try:
                print(f"\n--- Mengambil data dealer untuk organization: {org} ---")
                dealer_data = await service.aget_dealer_data(org)
                print(f"Success: {dealer_data.get('success', 'N/A')}")
                print(
                    f"Data keys: {list(dealer_data.keys()) if isinstance(dealer_data, dict) else 'Not a dict'}"
                )

                # Print sample data structure
                if isinstance(dealer_data, dict) and "data" in dealer_data:
                    data = dealer_data["data"]
                    if isinstance(data, list) and len(data) > 0:
                        print(f"Total dealers: {len(data)}")
                        print(
                            f"First dealer keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'N/A'}"
                        )
                    elif isinstance(data, dict):
                        print(f"Data structure keys: {list(data.keys())}")
                        # Print sample response for documentation
                        print(f"Sample response for {org}:")
                        import json

                        print(
                            json.dumps(dealer_data, indent=2, ensure_ascii=False)[:1000]
                            + "..."
                        )

            except Exception as e:
                print(f"Error untuk {org}: {e}")

        # Test untuk organization yang tidak valid
        try:
            print("\n--- Test dengan organization tidak valid ---")
            await service.aget_dealer_data("invalid_org")
        except ValueError as e:
            print(f"Expected ValueError: {e}")
        except Exception as e:
            print(f"Unexpected error: {e}")

    asyncio.run(main())
