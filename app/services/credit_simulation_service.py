import asyncio

from app.services.api_client import b2b_api_client


class CreditSimulationService:
    def __init__(self):
        self.url = "/price-list"
        self.params = {
            "leasingCode": "_GLOBAL_",
        }

    async def aget_credit_simulation(
        self, model_name: str, variant_code: str, city_group: str
    ):
        params = self.params.copy()
        params["modelName"] = model_name
        params["variantCode"] = variant_code
        params["cityGroup"] = city_group
        response = await b2b_api_client.get(self.url, params=params)
        response.raise_for_status()
        return response.json()


if __name__ == "__main__":

    async def main():
        service = CreditSimulationService()
        # Example usage
        simulation = await service.aget_credit_simulation(
            model_name="Vario 125",
            variant_code="L1F02N36S4A/T",
            city_group="cirebon",
        )
        print(simulation)

    asyncio.run(main())
