from typing import Dict, Optional
from app.services.api_client import ideal_api_client


async def turn_off_ai_reply(
    chat_room_path: str, conversation_resume: str
) -> Optional[Dict]:
    """
    Turn off AI reply for a specific chat room via API.

    Args:
        chat_room_path: The chat room path identifier

    Returns:
        API response JSON or None if error occurs
    """
    url = "/api/public/toggle-agent-ai-reply"
    payload = {
        "chatRoomPath": chat_room_path,
        "agentAiReply": False,
        "conversationResume": conversation_resume,
    }
    try:
        response = await ideal_api_client.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error turning off AI reply: {e}")
        return None


if __name__ == "__main__":
    import asyncio

    async def main():
        # Example usage
        result = await turn_off_ai_reply(
            chat_room_path="/projects/WLdKug7hau0MbRzKcnqg/chat_rooms/6285763705624",
            conversation_resume="User meminta berbicara dengan customer service setelah menanyakan produk.",
        )
        print(result)

    asyncio.run(main())
