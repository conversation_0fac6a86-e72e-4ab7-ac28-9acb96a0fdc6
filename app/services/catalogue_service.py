import asyncio
from typing import List, Dict, Any

from app.services.api_client import amarta_api_client


class CatalogueService:
    """Service untuk mengambil data katalog produk (model dan varian) dari API <PERSON>ta."""

    def __init__(self, organization: str = "amarta"):
        self.organization = organization

    async def aget_models(self, city_group: str) -> Dict[str, Any]:
        """
        Mengambil data model kendaraan secara asynchronous berdasarkan city group.

        Args:
            city_group (str): Grup kota untuk mengambil data model

        Returns:
            Dict[str, Any]: Response JSON dari API yang berisi data model

        Raises:
            httpx.HTTPStatusError: Jika terjadi error HTTP
        """
        model_url_template = "/products/{organization}/citygroup/{city_group}/model"
        url = model_url_template.format(
            city_group=city_group, organization=self.organization
        )
        response = await amarta_api_client.get(url)
        response.raise_for_status()
        return response.json()

    async def aget_variants(
        self, city_group: str, model_names: List[str]
    ) -> Dict[str, Any]:
        """
        Mengambil data varian kendaraan secara asynchronous berdasarkan city group dan model names.

        Args:
            city_group (str): Grup kota untuk mengambil data varian
            model_names (List[str]): List nama model untuk mengambil varian

        Returns:
            Dict[str, Any]: Response JSON yang berisi semua data varian dari model yang diminta

        Raises:
            httpx.HTTPStatusError: Jika terjadi error HTTP
        """
        tasks = []
        for model_name in model_names:
            url = f"/products/{self.organization}/citygroup/{city_group}/variant?model_name={model_name}"
            tasks.append(amarta_api_client.get(url))

        responses = await asyncio.gather(*tasks)

        all_variants = []
        for response in responses:
            response.raise_for_status()
            all_variants.extend(response.json().get("data", []))

        return {"data": all_variants}


if __name__ == "__main__":

    async def main():
        # Example usage dengan default organization (amarta)
        service = CatalogueService()

        # Example usage dengan organization khusus
        vinfast_service = CatalogueService(organization="vinfast")

        try:
            print("--- Mengambil data model (amarta) ---")
            models = await service.aget_models(city_group="JAWABARAT")
            print(f"Total models: {len(models.get('data', []))}")

            print("\n--- Mengambil data model (vinfast) ---")
            vinfast_models = await vinfast_service.aget_models(city_group="JAWABARAT")
            print(f"Total vinfast models: {len(vinfast_models.get('data', []))}")

            print("\n--- Mengambil data varian ---")
            variants = await service.aget_variants(
                city_group="JAWABARAT", model_names=["VGREENAC"]
            )
            print(f"Total variants: {len(variants.get('data', []))}")

        except Exception as e:
            print(f"Error: {e}")

    asyncio.run(main())
