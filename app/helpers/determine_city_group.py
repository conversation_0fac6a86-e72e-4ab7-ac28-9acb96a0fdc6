from textwrap import dedent

import devtools
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import Runnable, RunnableConfig

from app.llm.gemini import gemini
from app.services.city_group_service import CityGroupService
from app.structured_outputs.area_output import AreaOutput


def _prepare_city_group_determination_chain() -> Runnable:
    """Prepares the LangChain runnable for city group determination."""
    system_prompt = dedent(
        """
    -   Saya akan memberikan suatu alamat, tugas kamu adalah menentukan alamat ini masuk ke area mana.

    -   Daftar 'Area' yang valid adalah:
        {city_groups}

    -   Tahapannya adalah:
        -   Ini memerlukan pengetahuan internal kamu
        -   Parsing kecamatan, kota/kabupaten dan provinsi nya.
        -   Lakukan analisis scoring berdasarkan kecamatan, kota/kabupaten, dan provinsi.
        -   Pilih area dengan skor tertinggi.
        -   <PERSON>an ragu gunakan intuisi kamu

    -   Sekarang tentukan Area dari alamat ini:
        {address}
    """
    )

    model = gemini.with_structured_output(AreaOutput)
    return PromptTemplate.from_template(system_prompt) | model


async def adetermine_city_group(address: str, config: RunnableConfig = None):
    """Asynchronously determines the city group for a given address."""
    organization = config.get("configurable", {}).get("organization", "amarta")
    city_group_service = CityGroupService(org=organization)
    get_city_groups = await city_group_service.aget_city_groups()
    city_groups = [city_group["city_group"] for city_group in get_city_groups["data"]]
    city_groups_stringify = ",".join(city_groups)

    chain = _prepare_city_group_determination_chain()
    response = await chain.ainvoke(
        {
            "city_groups": city_groups_stringify,
            "address": address,
        },
        config=config,
    )

    return response


if __name__ == "__main__":
    import asyncio

    config = RunnableConfig(configurable={"organization": "amarta"})

    async def main():
        response = await adetermine_city_group("Ciamis", config=config)
        devtools.debug(response)

    asyncio.run(main())
