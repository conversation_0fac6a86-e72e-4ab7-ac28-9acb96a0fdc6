from langchain_core.load import dumpd, load
from langchain_core.messages import BaseMessage

from app.services.firebase_service import afirestore_client


async def replace_ai_agent_chat_room(
    chatroom_doc_path: str,
    messages: list,
):
    db = afirestore_client()
    chatroom_doc_path = chatroom_doc_path.lstrip("/")
    chat_room_doc = db.document(chatroom_doc_path)
    agent_chats = chat_room_doc.collection("ai_agent_chats")
    batch = db.batch()

    # Delete all existing documents in ai_agent_chats collection first
    existing_docs = agent_chats.stream()
    async for doc in existing_docs:
        batch.delete(doc.reference)

    for index, message in enumerate(messages):
        new_doc_ref = agent_chats.document()
        batch.set(new_doc_ref, {"langchain_message": dumpd(message), "order": index})

    await batch.commit()


async def load_ai_agent_chat_room(
    chatroom_doc_path: str,
) -> list[BaseMessage]:
    db = afirestore_client()
    chatroom_doc_path = chatroom_doc_path.lstrip("/")
    chat_room_doc = db.document(chatroom_doc_path)
    agent_chats = chat_room_doc.collection("ai_agent_chats")
    agent_chats = agent_chats.order_by("order", direction="ASCENDING")
    docs = agent_chats.stream()
    base_messages: list = []
    async for chat in docs:
        data = chat.to_dict()
        base_messages.append(load(data["langchain_message"]))

    return base_messages


async def load_chat_room_messages(
    chatroom_doc_path: str,
) -> list:
    db = afirestore_client()
    chatroom_doc_path = chatroom_doc_path.lstrip("/")
    chat_room_doc = db.document(chatroom_doc_path)
    agent_chats = chat_room_doc.collection("chats")
    agent_chats = agent_chats.order_by("message.unixtime", direction="ASCENDING")
    docs = agent_chats.stream()
    base_messages: list = []
    async for chat in docs:
        data = chat.to_dict()
        message = data.get("message")
        origin = data.get("origin")
        role = "user"
        if origin.get("id") == "Assistant AI":
            role = "assistant"
        else:
            if message.get("direction") == "OUT":
                role = f"Admin: {origin.get('display_name')}"

        base_messages.append(
            {
                "content": message,
                "role": role,
            }
        )

    return base_messages
