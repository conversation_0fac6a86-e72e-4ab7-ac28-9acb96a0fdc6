"""
Shared utilities for plan execution tracking and management.

This module provides common functionality for tracking plan execution status,
determining completion state, and managing continuous execution logic.
"""

from typing import List, Optional


# Removed PlanStep, PlanExecutionTracker, and parse_finished_plan_status classes/functions
# as they are no longer needed with the simplified implementation


def format_execution_status_message(
    intent: str,
    plans: List[str],
    finished_plans: List[str],
    tool_calling_now: Optional[str] = None,
    plan_now: Optional[str] = None,
) -> str:
    """
    Format an LLM instruction message with execution status parameters.

    Args:
        intent: Ringkasan keinginan/masalah user
        plans: Daftar rencana yang harus dieksekusi
        finished_plans: Daftar rencana yang sudah selesai
        tool_calling_now: Tool yang harus dipanggil sekarang
        plan_now: Rencana yang sekarang dilakukan

    Returns:
        Formatted LLM instruction message
    """
    # Format plans
    plans_text = "\n".join([f"  - {plan}" for plan in plans])

    # Format finished plans
    finished_plans_text = (
        "\n".join([f"  - {fp}" for fp in finished_plans])
        if finished_plans
        else "  - Belum ada yang selesai"
    )

    # Format plan now
    plan_now_text = f"  - {plan_now}" if plan_now else "  - Tidak ada"

    # Base message dengan sudut pandang LLM
    message = f"""TUJUAN: {intent}

RENCANA KERJA (yang perlu saya lakukan):
{plans_text}

PROGRES SELESAI (yang sudah saya kerjakan):
{finished_plans_text}

PROGRES SEKARANG (yang sedang saya kerjakan):
{plan_now_text}
"""

    # Add tool calling instruction if specified
    if tool_calling_now:
        message += f"\n\nLANGKAH BERIKUTNYA: {tool_calling_now}"
        message += f"\n\nSTATUS: Saya akan menjalankan tool '{tool_calling_now}' sekarang untuk melanjutkan eksekusi rencana yang belum selesai."
    else:
        message += "\n\nSTATUS: Saya akan melanjutkan eksekusi rencana yang belum selesai secara sistematis."

    return message


# Removed determine_execution_action function as it's no longer needed
