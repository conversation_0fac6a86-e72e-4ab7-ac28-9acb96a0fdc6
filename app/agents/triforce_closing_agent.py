import threading
from typing import Dict, Any

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnableConfig
from langgraph.constants import START, END
from langgraph.graph import StateGraph

from app.agents.states.triforce_closing_agent_state import TriforceClosingAgentState
from app.llm.gemini import gemini


class TriforceClosingAgent:
    """Triforce Closing Agent - Specialized AI agent for sales closing conversations."""

    _instance = None
    _lock = threading.Lock()
    _initialized = False
    _runnable_instance = None

    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if not cls._instance:
                cls._instance = super().__new__(cls)
            return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._initialized = True
        self._build_graph()

    def _build_graph(self):
        """Build the LangGraph state graph for Triforce Closing Agent."""

        # Create the state graph
        workflow = StateGraph(TriforceClosingAgentState)

        # Add nodes
        workflow.add_node("analyze_prospect", self._analyze_prospect_node)
        workflow.add_node("closing_strategy", self._closing_strategy_node)
        workflow.add_node("finalize", self._finalize_node)

        # Add edges
        workflow.add_edge(START, "analyze_prospect")
        workflow.add_edge("analyze_prospect", "closing_strategy")
        workflow.add_edge("closing_strategy", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self._runnable_instance = workflow.compile()

    async def _get_model(self, config: RunnableConfig) -> Any:
        """Get the appropriate LLM model."""
        # Default to Gemini model
        return gemini

    async def _analyze_prospect_node(self, state: TriforceClosingAgentState, config: RunnableConfig):
        """Analyze prospect's interest level and current stage."""

        # Get the model
        model = await self._get_model(config)

        # Create analysis prompt
        analysis_prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_analysis_prompt()),
            ("placeholder", "{messages}")
        ])

        # Create the chain
        chain = analysis_prompt | model

        # Get messages from state
        messages = state["messages"]

        # Invoke the model for analysis
        analysis_response = await chain.ainvoke({"messages": messages}, config)

        # Extract analysis (simplified for basic version)
        analysis_content = analysis_response.content if hasattr(analysis_response, "content") else str(analysis_response)
        
        # Basic analysis extraction (can be enhanced with structured output)
        interest_level = "medium"  # Default
        closing_stage = "qualifying"  # Default
        
        if "sangat tertarik" in analysis_content.lower() or "very interested" in analysis_content.lower():
            interest_level = "very_high"
            closing_stage = "closing"
        elif "tertarik" in analysis_content.lower() or "interested" in analysis_content.lower():
            interest_level = "high"
            closing_stage = "presenting"
        elif "ragu" in analysis_content.lower() or "hesitant" in analysis_content.lower():
            interest_level = "low"
            closing_stage = "handling_objections"

        # Update state
        updated_state = {
            "prospect_interest_level": interest_level,
            "closing_stage": closing_stage,
        }

        return updated_state

    async def _closing_strategy_node(self, state: TriforceClosingAgentState, config: RunnableConfig):
        """Generate closing strategy based on prospect analysis."""

        # Get the model
        model = await self._get_model(config)

        # Create closing strategy prompt
        strategy_prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_closing_strategy_prompt()),
            ("placeholder", "{messages}")
        ])

        # Create the chain
        chain = strategy_prompt | model

        # Get messages from state
        messages = state["messages"]
        
        # Add context about current analysis
        context_message = HumanMessage(content=f"""
        Konteks Analisis Prospek:
        - Tingkat Minat: {state.get('prospect_interest_level', 'unknown')}
        - Tahap Closing: {state.get('closing_stage', 'initial')}
        
        Berikan respons closing yang sesuai dengan konteks ini.
        """)
        
        messages_with_context = messages + [context_message]

        # Invoke the model
        response = await chain.ainvoke({"messages": messages_with_context}, config)

        # Update state
        updated_state = {
            "messages": [response],
            "last_response": (
                response.content if hasattr(response, "content") else str(response)
            ),
        }

        return updated_state

    async def _finalize_node(self, state: TriforceClosingAgentState):
        """Finalize the conversation state."""
        return {"is_active": True}

    def _get_analysis_prompt(self) -> str:
        """Get the analysis prompt for prospect evaluation."""
        return """
Anda adalah ahli analisis prospek penjualan. Analisis pesan dari prospek untuk menentukan:

1. Tingkat minat (rendah, sedang, tinggi, sangat tinggi)
2. Tahap dalam proses penjualan
3. Objeksi yang mungkin ada
4. Sinyal buying yang terdeteksi

Berikan analisis singkat dan akurat berdasarkan komunikasi prospek.
"""

    def _get_closing_strategy_prompt(self) -> str:
        """Get the closing strategy prompt for sales conversations."""
        return """
Anda adalah Triforce Closing Agent 🎯, ahli closing penjualan yang handal dan profesional.

IDENTITAS & MISI:
🎯 Spesialis closing dengan teknik Triforce (Trust, Value, Urgency)
🎯 Membantu prospek membuat keputusan terbaik
🎯 Fokus pada win-win solution

TEKNIK TRIFORCE CLOSING:
1️⃣ TRUST (Kepercayaan)
   • Bangun rapport dan kredibilitas
   • Tunjukkan pemahaman mendalam tentang kebutuhan
   • Berikan testimoni dan bukti sosial

2️⃣ VALUE (Nilai)
   • Jelaskan manfaat konkret dan ROI
   • Bandingkan dengan alternatif lain
   • Fokus pada solusi masalah spesifik

3️⃣ URGENCY (Urgensi)
   • Ciptakan sense of urgency yang natural
   • Tunjukkan opportunity cost jika menunda
   • Berikan limited time offer yang masuk akal

STRATEGI KOMUNIKASI WHATSAPP:
📱 Maksimal 4096 karakter per pesan
📱 Gunakan emoji untuk emphasis
📱 Format mudah dibaca dengan bullet points
📱 Gunakan pertanyaan closing yang efektif

TEKNIK CLOSING YANG DIGUNAKAN:
🔥 Assumptive Close: "Kapan kita bisa mulai implementasi?"
🔥 Alternative Close: "Lebih prefer paket A atau B?"
🔥 Urgency Close: "Promo ini berakhir hari ini"
🔥 Trial Close: "Bagaimana menurut Anda sejauh ini?"
🔥 Summary Close: "Jadi benefit utamanya adalah..."

HANDLING OBJECTIONS:
❌ "Harga terlalu mahal" → Fokus pada ROI dan value
❌ "Perlu pikir dulu" → Bantu identifikasi keraguan spesifik
❌ "Belum ada budget" → Tunjukkan cost of inaction
❌ "Perlu diskusi tim" → Tawarkan presentasi untuk tim

CONTOH RESPONS CLOSING:
"Pak/Bu [Nama], berdasarkan diskusi kita:

✅ Anda butuh solusi untuk [masalah spesifik]
✅ Budget sudah tersedia [jumlah]
✅ Timeline implementasi [waktu]

Saya yakin [produk/layanan] adalah solusi tepat karena:
🎯 [Benefit 1 spesifik]
🎯 [Benefit 2 dengan angka]
🎯 [Benefit 3 jangka panjang]

Khusus hari ini, ada promo [detail promo].

Bagaimana kalau kita finalisasi sekarang? 🤝"

PRINSIP UTAMA:
✅ Selalu dengarkan dulu, baru bicara
✅ Fokus pada kebutuhan, bukan fitur
✅ Gunakan data dan fakta, bukan asumsi
✅ Ciptakan urgency yang natural dan etis
✅ Jaga hubungan jangka panjang

Respons harus persuasif namun tidak pushy, profesional namun hangat.
"""

    @property
    def runnable(self):
        """Get the compiled runnable graph."""
        if self._runnable_instance is None:
            self._build_graph()
        return self._runnable_instance
