import threading
from typing import Dict, Any

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnableConfig
from langgraph.constants import START, END
from langgraph.graph import StateGraph

from app.agents.states.agent_trimitra_state import AgentTrimitraState
from app.llm.gemini import gemini


class AgentTrimitra:
    """Agent Trimitra - A general purpose conversational AI agent."""

    _instance = None
    _lock = threading.Lock()
    _initialized = False
    _runnable_instance = None

    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if not cls._instance:
                cls._instance = super().__new__(cls)
            return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._initialized = True
        self._build_graph()

    def _build_graph(self):
        """Build the LangGraph state graph for Agent Trimitra."""

        # Create the state graph
        workflow = StateGraph(AgentTrimitraState)

        # Add nodes
        workflow.add_node("chat_model", self._chat_model_node)
        workflow.add_node("finalize", self._finalize_node)

        # Add edges
        workflow.add_edge(START, "chat_model")
        workflow.add_edge("chat_model", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self._runnable_instance = workflow.compile()

    async def _get_model(self, config: RunnableConfig) -> Any:
        """Get the appropriate LLM model."""
        # Default to Gemini model
        return gemini

    async def _chat_model_node(self, state: AgentTrimitraState, config: RunnableConfig):
        """Main chat model node for processing user messages."""

        # Get the model
        model = await self._get_model(config)

        # Create system prompt
        system_prompt = ChatPromptTemplate.from_messages(
            [("system", self._get_system_prompt()), ("placeholder", "{messages}")]
        )

        # Create the chain
        chain = system_prompt | model

        # Get messages from state
        messages = state["messages"]

        # Invoke the model
        response = await chain.ainvoke({"messages": messages}, config)

        # Update state
        updated_state = {
            "messages": [response],
            "last_response": (
                response.content if hasattr(response, "content") else str(response)
            ),
        }

        return updated_state

    async def _finalize_node(self, state: AgentTrimitraState):
        """Finalize the conversation state."""
        return {"is_active": True}

    def _get_system_prompt(self) -> str:
        """Get the system prompt for Agent Trimitra optimized for WhatsApp."""
        return """
Anda adalah Agent Trimitra 🤖, asisten AI yang ramah dan membantu di WhatsApp.

KARAKTERISTIK:
✅ Ramah dan sopan dalam berkomunikasi
✅ Memberikan jawaban informatif dan akurat
✅ Menggunakan bahasa Indonesia yang baik
✅ Memahami kebutuhan pengguna dengan baik

FORMAT RESPONS WHATSAPP:
📝 Maksimal 4096 karakter per pesan
📝 Gunakan emoji untuk memperjelas pesan
📝 Hindari markdown kompleks (bold, italic berlebihan)
📝 Gunakan bullet points sederhana (- atau •)
📝 Pisahkan informasi dengan baris kosong
📝 Gunakan numbering untuk langkah-langkah (1. 2. 3.)

TUGAS UTAMA:
🎯 Jawab pertanyaan dengan jelas dan ringkas
🎯 Berikan bantuan sesuai kemampuan
🎯 Jaga percakapan positif dan konstruktif
🎯 Sampaikan ketidakpastian dengan jujur
🎯 Gunakan format yang mudah dibaca di WhatsApp

CONTOH FORMAT RESPONS:
"Halo! 👋 Saya Agent Trimitra.

Untuk pertanyaan Anda:
• Poin pertama
• Poin kedua

Semoga membantu! 😊"

Selalu profesional namun tetap hangat dalam setiap interaksi WhatsApp.
"""

    @property
    def runnable(self):
        """Get the compiled runnable graph."""
        if self._runnable_instance is None:
            self._build_graph()
        return self._runnable_instance
