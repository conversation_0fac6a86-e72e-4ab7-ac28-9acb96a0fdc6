import threading  # Import threading for thread safety

import pendulum
from langchain_core.messages import AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnableConfig  # Import Runnable
from langchain_core.tools import BaseTool
from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolN<PERSON>, tools_condition

from app.agents.prompts.amartamobil.whatsapp_basic_agent_prompts_v1 import (
    SYSTEM_PROMPT as SYSTEM_PROMPT_AMARTAMOBIL,
)
from app.agents.prompts.amartamotor.whatsapp_basic_agent_prompts_v2 import (
    SYSTEM_PROMPT as SYSTEM_PROMPT_AMARTAMOTOR,
)
from app.agents.states.agent_basic_state import AgentBasicState
from app.services.catalogue_service import CatalogueService
from app.tools.ai_final_answer_tool import ai_final_answer_tool
from app.tools.connect_to_admin_tool import connect_to_admin_tool
from app.tools.get_ad_information_tool import get_ad_information_tool
from app.tools.get_credit_simulation_tool_and_promo import (
    get_credit_simulation_and_promo_tool,
)
from app.tools.get_variants_tool import get_variants_tool
from app.tools.get_vehicle_promo_tool import get_vehicle_promo_tool
from app.tools.update_user_information_tool import (
    update_user_information_tool,
)
from app.tools.get_dealer_data_tool import get_dealer_data_tool


class WhatsappBasicAgentChat:
    """A basic agent that chats with the user."""

    _instance = None
    _lock = threading.Lock()  # Lock for thread-safe instance creation
    _initialized = False  # Flag to ensure __init__ runs only once
    _runnable_instance = None  # To store the compiled runnable

    def __new__(cls, *args, **kwargs):
        with cls._lock:  # Acquire lock for thread safety
            if not cls._instance:
                cls._instance = super().__new__(cls)
            return cls._instance

    __tools: list[BaseTool] = [
        update_user_information_tool,
        get_ad_information_tool,
        get_variants_tool,
        get_credit_simulation_and_promo_tool,
        get_vehicle_promo_tool,
        get_dealer_data_tool,
        connect_to_admin_tool,
        ai_final_answer_tool,
    ]

    def __init__(self):
        with WhatsappBasicAgentChat._lock:  # Acquire lock for __init__ as well
            if not self._initialized:  # Only initialize if not already initialized
                graph = StateGraph(AgentBasicState)

                graph.add_node(
                    "chat_model",
                    self.__chat_model_node,
                )

                tool_node = ToolNode(tools=self.__tools)
                graph.add_node("calling_tools", tool_node)
                graph.add_node("finish", self.__finish)

                graph.add_conditional_edges(
                    source="chat_model",
                    path=tools_condition,
                    path_map={
                        "tools": "calling_tools",
                        "__end__": "finish",
                    },
                )

                graph.add_conditional_edges(
                    source="calling_tools",
                    path=self.__check_final_answer,
                    path_map={
                        "chat_model": "chat_model",
                        "__end__": "finish",
                    },
                )

                graph.add_edge(START, "chat_model")
                graph.add_edge("finish", END)

                self._runnable_instance = graph.compile()
                self._initialized = True  # Set flag to true after initialization

    async def __check_final_answer(self, state: AgentBasicState):
        if state.get("final_answer", None):
            return "__end__"
        return "chat_model"

    async def __get_models(self, config: RunnableConfig) -> str:
        model_city_group = config.get("configurable", {}).get(
            "model_city_group", "bandung"
        )
        organization = config.get("configurable", {}).get("organization", "amarta")
        catalogue_service = CatalogueService(organization=organization)
        get_models = await catalogue_service.aget_models(
            model_city_group,
        )
        available_models = get_models.get("data", [])
        model_names = [model.get("model_name", "") for model in available_models]

        models = ""
        # loop with numbered list
        for i, model in enumerate(model_names):
            models += f"{i + 1}. {model}\n"
        return models

    async def __chat_model_node(self, state: AgentBasicState, config: RunnableConfig):
        system_prompt = SYSTEM_PROMPT_AMARTAMOTOR
        tools = []
        organization = config.get("configurable", {}).get("organization", "amarta")
        if organization == "amarta":
            system_prompt = SYSTEM_PROMPT_AMARTAMOTOR
            tools = [
                update_user_information_tool,
                get_ad_information_tool,
                get_variants_tool,
                get_credit_simulation_and_promo_tool,
                get_vehicle_promo_tool,
                get_dealer_data_tool,
                connect_to_admin_tool,
                ai_final_answer_tool,
            ]
        if organization == "vinfast":
            system_prompt = SYSTEM_PROMPT_AMARTAMOBIL
            tools = [
                update_user_information_tool,
                get_ad_information_tool,
                get_variants_tool,
                get_vehicle_promo_tool,
                get_dealer_data_tool,
                connect_to_admin_tool,
                ai_final_answer_tool,
            ]

        tool_choice = "any"

        # if state.get("turn_completed", False):
        #     tool_choice = "auto"

        model = (
            config.get("configurable", {})
            .get("model")
            .bind_tools(
                tools,
                tool_choice=tool_choice,
            )
        )

        messages = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                *state["messages"],
            ]
        )

        models_str = await self.__get_models(config)

        pipe = messages | model
        response = await pipe.ainvoke(
            {
                "current_date": pendulum.now().to_iso8601_string(),
                "models": models_str,
                "real_name": state.get("real_name", "<null>"),
                "city_group": state.get("city_group", "<null>"),
                "connected_to_admin": state.get("connected_to_admin", False),
            }
        )
        return {"messages": [response]}

    async def __finish(self, state: AgentBasicState):
        messages = state["messages"]
        last_message = messages[-1]
        ai_message = AIMessage(content=last_message.content)
        messages.append(ai_message)
        del messages[-3]
        del messages[-2]
        return {"messages": messages}

    @property
    def runnable(self):
        return self._runnable_instance
