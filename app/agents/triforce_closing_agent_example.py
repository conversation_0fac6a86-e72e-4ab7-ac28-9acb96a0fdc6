"""
Example usage of Triforce Closing Agent
"""

import asyncio
from langchain_core.messages import HumanMessage
from app.agents.triforce_closing_agent import TriforceClosingAgent
from app.agents.states.triforce_closing_agent_state import TriforceClosingAgentState


async def test_triforce_closing_agent():
    """Test the Triforce Closing Agent with sample conversations."""
    
    # Initialize the agent
    agent = TriforceClosingAgent()
    
    # Test conversation 1: Interested prospect
    print("=== Test 1: Interested Prospect ===")
    
    initial_state = TriforceClosingAgentState(
        messages=[
            HumanMessage(content="Halo, saya tertarik dengan produk Anda. Bisa dijelaskan lebih detail?")
        ],
        thread_id="test_001",
        user_name="<PERSON><PERSON>"
    )
    
    try:
        result = await agent.runnable.ainvoke(initial_state)
        print(f"Response: {result['last_response']}")
        print(f"Interest Level: {result.get('prospect_interest_level', 'unknown')}")
        print(f"Closing Stage: {result.get('closing_stage', 'initial')}")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test conversation 2: Hesitant prospect
    print("=== Test 2: Hesitant Prospect ===")
    
    hesitant_state = TriforceClosingAgentState(
        messages=[
            HumanMessage(content="Hmm, saya masih ragu-ragu. Harganya agak mahal ya...")
        ],
        thread_id="test_002",
        user_name="Siti Aminah"
    )
    
    try:
        result = await agent.runnable.ainvoke(hesitant_state)
        print(f"Response: {result['last_response']}")
        print(f"Interest Level: {result.get('prospect_interest_level', 'unknown')}")
        print(f"Closing Stage: {result.get('closing_stage', 'initial')}")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(test_triforce_closing_agent())
