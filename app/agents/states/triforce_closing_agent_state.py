from langgraph.graph import MessagesState


class TriforceClosingAgentState(MessagesState):
    """State for the Triforce Closing Agent - specialized for sales closing conversations."""

    # Basic conversation state
    thread_id: str = ""
    user_name: str = ""

    # Agent status
    is_active: bool = True
    last_response: str = ""

    # Closing-specific state
    prospect_interest_level: str = "unknown"  # low, medium, high, very_high
    objections_raised: list = []
    closing_stage: str = "initial"  # initial, qualifying, presenting, handling_objections, closing, follow_up
    deal_value: str = ""
    next_action: str = ""
