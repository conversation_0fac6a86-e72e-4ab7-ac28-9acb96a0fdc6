from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointTuple
from langgraph.serde.base import Serializer
from app.services.firebase_service import afirestore_client, firestore_client
from google.cloud import firestore
import logging
from typing import Dict, Any, Iterator, AsyncIterator, Optional

from langchain_core.runnables import RunnableConfig


class FirestoreOperationError(Exception):
    """Custom exception for Firestore operations"""

    pass


class CheckpointNotFound(Exception):
    """Exception for when checkpoint is not found"""

    pass


class FirestoreCheckpointer(BaseCheckpointSaver):
    """
    A Firestore-based checkpointer for LangGraph that correctly handles serialization.
    """

    def __init__(
        self,
        collection_name: str = "checkpoints",
        *,
        serializer: Optional[Serializer] = None,
    ):
        """
        Initialize the Firestore checkpointer.

        Args:
            collection_name: The name of the Firestore collection.
            serializer: The serializer to use for checkpoints. Defaults to a new instance.
        """
        super().__init__(serializer=serializer)
        self.collection_name = collection_name
        self.async_client = afirestore_client()
        self.sync_client = firestore_client()

    def _get_tuple(self, data: Dict[str, Any]) -> CheckpointTuple:
        """Construct a CheckpointTuple from Firestore data, deserializing the checkpoint."""
        return CheckpointTuple(
            config=data["config"],
            checkpoint=self.serializer.loads(data["checkpoint"]),
            metadata=data.get("metadata"),
            parent_config=data.get("parent_config"),
        )

    def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        thread_id = config["configurable"]["thread_id"]
        checkpoint_id = config["configurable"].get("checkpoint_id")

        try:
            if checkpoint_id:
                doc_ref = self.sync_client.collection(self.collection_name).document(
                    checkpoint_id
                )
                doc = doc_ref.get()
                if not doc.exists:
                    return None
                data = doc.to_dict()
                if data["config"]["configurable"]["thread_id"] != thread_id:
                    raise ValueError("Thread ID mismatch")
            else:
                query = (
                    self.sync_client.collection(self.collection_name)
                    .where("config.configurable.thread_id", "==", thread_id)
                    .order_by("checkpoint.ts", direction=firestore.Query.DESCENDING)
                    .limit(1)
                )
                docs = list(query.stream())
                if not docs:
                    return None
                data = docs[0].to_dict()

            return self._get_tuple(data)
        except Exception as e:
            logging.error(f"Firestore sync get_tuple failed: {str(e)}")
            raise FirestoreOperationError("Checkpoint retrieval failed") from e

    async def aget_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        thread_id = config["configurable"]["thread_id"]
        checkpoint_id = config["configurable"].get("checkpoint_id")

        try:
            if checkpoint_id:
                doc_ref = self.async_client.collection(self.collection_name).document(
                    checkpoint_id
                )
                doc = await doc_ref.get()
                if not doc.exists:
                    return None
                data = doc.to_dict()
                if data["config"]["configurable"]["thread_id"] != thread_id:
                    raise ValueError("Thread ID mismatch")
            else:
                query = (
                    self.async_client.collection(self.collection_name)
                    .where("config.configurable.thread_id", "==", thread_id)
                    .order_by("checkpoint.ts", direction=firestore.Query.DESCENDING)
                    .limit(1)
                )
                docs = [doc async for doc in query.stream()]
                if not docs:
                    return None
                data = docs[0].to_dict()

            return self._get_tuple(data)
        except Exception as e:
            logging.error(f"Firestore async aget_tuple failed: {str(e)}")
            raise FirestoreOperationError("Async checkpoint retrieval failed") from e

    def list(
        self,
        config: RunnableConfig,
        *,
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[RunnableConfig] = None,
        limit: Optional[int] = None,
    ) -> Iterator[CheckpointTuple]:
        thread_id = config["configurable"]["thread_id"]
        try:
            query = (
                self.sync_client.collection(self.collection_name)
                .where("config.configurable.thread_id", "==", thread_id)
                .order_by("checkpoint.ts", direction=firestore.Query.DESCENDING)
            )

            if before:
                before_checkpoint_id = before["configurable"]["checkpoint_id"]
                before_doc = (
                    self.sync_client.collection(self.collection_name)
                    .document(before_checkpoint_id)
                    .get()
                )
                if before_doc.exists:
                    before_ts = before_doc.to_dict()["checkpoint"]["ts"]
                    query = query.where("checkpoint.ts", "<", before_ts)

            if limit:
                query = query.limit(limit)

            for doc in query.stream():
                yield self._get_tuple(doc.to_dict())
        except Exception as e:
            logging.error(f"Firestore sync list failed: {str(e)}")
            raise FirestoreOperationError("Checkpoint listing failed") from e

    async def alist(
        self,
        config: RunnableConfig,
        *,
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[RunnableConfig] = None,
        limit: Optional[int] = None,
    ) -> AsyncIterator[CheckpointTuple]:
        thread_id = config["configurable"]["thread_id"]
        try:
            query = (
                self.async_client.collection(self.collection_name)
                .where("config.configurable.thread_id", "==", thread_id)
                .order_by("checkpoint.ts", direction=firestore.Query.DESCENDING)
            )

            if before:
                before_ts = before["configurable"]["checkpoint_id"]
                query = query.where("checkpoint.ts", "<", before_ts)

            if limit:
                query = query.limit(limit)

            async for doc in query.stream():
                yield self._get_tuple(doc.to_dict())
        except Exception as e:
            logging.error(f"Firestore async alist failed: {str(e)}")
            raise FirestoreOperationError("Async checkpoint listing failed") from e

    def put(
        self, config: RunnableConfig, checkpoint: Checkpoint, metadata: Dict[str, Any]
    ) -> RunnableConfig:
        checkpoint_id = config["configurable"]["checkpoint_id"]
        try:
            doc_ref = self.sync_client.collection(self.collection_name).document(
                checkpoint_id
            )
            doc_ref.set(
                {
                    "checkpoint": self.serializer.dumps(checkpoint),
                    "metadata": metadata,
                    "config": config,
                    "parent_config": self.get_parent_config(config, checkpoint),
                }
            )
            return config
        except Exception as e:
            logging.error(f"Firestore sync put failed: {str(e)}")
            raise FirestoreOperationError("Checkpoint save failed") from e

    async def aput(
        self, config: RunnableConfig, checkpoint: Checkpoint, metadata: Dict[str, Any]
    ) -> RunnableConfig:
        checkpoint_id = config["configurable"]["checkpoint_id"]
        try:
            doc_ref = self.async_client.collection(self.collection_name).document(
                checkpoint_id
            )
            await doc_ref.set(
                {
                    "checkpoint": self.serializer.dumps(checkpoint),
                    "metadata": metadata,
                    "config": config,
                    "parent_config": await self.aget_parent_config(config, checkpoint),
                }
            )
            return config
        except Exception as e:
            logging.error(f"Firestore async aput failed: {str(e)}")
            raise FirestoreOperationError("Async checkpoint save failed") from e
