import textwrap
from typing import Optional

from langchain_core.messages import ToolMessage, AIMessage
from langchain_core.tools import StructuredTool, ToolException, InjectedToolCallId
from langgraph.types import Command
from pydantic import BaseModel, Field
from typing_extensions import Annotated

_DESCRIPTION = textwrap.dedent(
    """
Tool untuk memberikan jawaban final yang komprehensif kepada user.

Tool ini digunakan untuk:
- Memberikan jawaban akhir yang lengkap dan terstruktur
- Menyajikan kesimpulan yang jelas dan mudah dipahami
- Mengakhiri percakapan dengan jawaban yang memuaskan

Input yang diperlukan:
- final_answer: Jawaban final yang akan diberikan kepada user (string, wajib diisi)

Output:
- Jawaban final yang terformat dengan baik

CONTOH PENGGUNAAN:
- Memberikan jawaban final setelah simulasi kredit
- Merangkum informasi varian motor yang tersedia
- Memberikan kesimpulan dari pencarian informasi
- Menyajikan rekomendasi produk yang sesuai
"""
)


class AIFinalAnswerSchema(BaseModel):
    """
    Schema untuk input parameter tool ai_final_answer.

    Attributes:
        final_answer (str): Jawaban final lengkap yang akan diberikan kepada user.
    """

    final_answer: str = Field(
        ...,
        description="Jawaban final lengkap untuk customer",
        min_length=50,
    )
    tool_call_id: Annotated[str, InjectedToolCallId]


def _format_final_answer(final_answer: str) -> str:
    """
    Format jawaban final menjadi output yang terstruktur.

    Args:
        final_answer (str): Jawaban final utama

    Returns:
        str: Jawaban final yang terformat
    """
    output_parts = []

    # Jawaban utama
    output_parts.append(final_answer)

    return "\n".join(output_parts)


async def _aai_final_answer_tool(
    final_answer: str,
    tool_call_id: Annotated[str, InjectedToolCallId] = None,
) -> Command:
    """
    Tool untuk memberikan jawaban final yang komprehensif kepada user.

    Args:
        final_answer (str): Jawaban final yang akan diberikan kepada user
        tool_call_id (str): ID dari tool call yang diinjeksi otomatis

    Returns:
        Command: Command object dengan update state dan tool message

    Raises:
        ToolException: Jika terjadi error dalam pemrosesan
    """
    try:
        # Validasi input
        if not final_answer or not final_answer.strip():
            raise ToolException("final_answer tidak boleh kosong")

        # Bersihkan input
        final_answer = final_answer.strip()

        # Format jawaban final
        formatted_answer = _format_final_answer(final_answer=final_answer)

        # Buat tool message
        tool_message = ToolMessage(
            content=formatted_answer,
            tool_call_id=tool_call_id,
        )

        # Return Command dengan update state
        return Command(
            update={
                "messages": [tool_message],
                "final_answer": final_answer,
                "turn_completed": True,
                "goto": "finalized",
            },
        )

    except ToolException:
        raise
    except Exception as e:
        error_msg = f"Gagal memproses jawaban final: {str(e)}"
        raise ToolException(error_msg)


ai_final_answer_tool = StructuredTool.from_function(
    name="ai_final_answer",
    coroutine=_aai_final_answer_tool,
    args_schema=AIFinalAnswerSchema,
    description=_DESCRIPTION,
    return_direct=True,
)
