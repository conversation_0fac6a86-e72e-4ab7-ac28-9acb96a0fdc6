import textwrap
from langchain_core.messages import ToolMessage
from langchain_core.tools import StructuredTool, ToolException, InjectedToolCallId
from langgraph.types import Command
from pydantic import BaseModel
from typing_extensions import Annotated

_DESCRIPTION = textwrap.dedent(
    """
Tool untuk menandai bahwa agent sudah selesai gilirannya dalam turn-based conversation.

Tool ini digunakan untuk:
- Menandai bahwa giliran agent sudah selesai
- Memberikan sinyal bahwa agent siap memberikan final answer
- Mengatur state untuk transisi ke tahap final answer

Tidak ada input yang diperlukan.

Output:
- Status konfirmasi bahwa giliran agent sudah selesai

CONTOH PENGGUNAAN:
- <PERSON><PERSON>h selesai mengumpulkan informasi yang diperlukan
- Setelah menyelesaikan simulasi atau kalkulasi
- Sebelum memberikan jawaban final kepada user
- Ketika semua langkah dalam workflow sudah completed
"""
)


class AgentTurnCompleteSchema(BaseModel):
    """
    Schema untuk tool agent_turn_complete.

    Tool ini tidak memerlukan parameter input.
    """

    tool_call_id: Annotated[str, InjectedToolCallId]


def _format_turn_complete_message() -> str:
    """
    Format pesan konfirmasi bahwa giliran agent sudah selesai.

    Returns:
        str: Pesan konfirmasi yang terformat
    """
    output_parts = []

    # Pesan konfirmasi
    output_parts.append("✅ **GILIRAN SELESAI**")
    output_parts.append(
        "Agent telah menyelesaikan gilirannya dan siap memberikan jawaban final."
    )

    return "\n".join(output_parts)


async def _aagent_turn_complete_tool(
    tool_call_id: Annotated[str, InjectedToolCallId] = None,
) -> Command:
    """
    Tool untuk menandai bahwa agent sudah selesai gilirannya dalam turn-based conversation.

    Args:
        tool_call_id (str): ID dari tool call yang diinjeksi otomatis

    Returns:
        Command: Command object dengan update state dan tool message

    Raises:
        ToolException: Jika terjadi error dalam pemrosesan
    """
    try:
        # Format pesan konfirmasi
        formatted_message = _format_turn_complete_message()

        # Buat tool message
        tool_message = ToolMessage(
            content=formatted_message,
            tool_call_id=tool_call_id,
        )

        # Return Command dengan update state
        return Command(
            update={
                "messages": [tool_message],
                "turn_completed": True,
            },
        )

    except Exception as e:
        error_msg = f"Gagal menandai giliran selesai: {str(e)}"
        raise ToolException(error_msg)


agent_turn_complete_tool = StructuredTool.from_function(
    name="agent_turn_complete",
    coroutine=_aagent_turn_complete_tool,
    args_schema=AgentTurnCompleteSchema,
    description=_DESCRIPTION,
    return_direct=False,
)
