import logging
import textwrap
from enum import Enum
from textwrap import dedent
from typing import Optional

import httpx
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import StructuredTool, ToolException
from pydantic import BaseModel, Field

from app.services.credit_simulation_service import CreditSimulationService
from app.services.get_promo_service import GetPromoService


class CreditSimulationMode(str, Enum):
    GET_SUMMARY = "get_summary"
    GET_SIMULATION = "get_simulation"


class NotAvailableCreditScheme(Exception):
    def __init__(
        self, message="Tidak ada skema kredit yang cocok dengan kriteria yang diminta."
    ):
        super().__init__(message)


_DESCRIPTION = textwrap.dedent(
    """
Mengambil simulasi kredit kendaraan dan data promo berdasarkan parameter yang ditentukan.

Tool ini digunakan untuk:
- Mendapatkan simulasi kredit motor dengan berbagai opsi DP, tenor, dan cicilan
- Mengambil data promo yang tersedia untuk model dan varian tertentu
- Menampilkan ringkasan rentang DP, tenor, dan cicilan yang tersedia
- Memfilter simulasi kredit berdasarkan kriteria range yang diberikan
- Memberikan informasi detail cicilan bulanan dan total pembayaran
- Menampilkan opsi kredit yang sesuai dengan budget pengguna

Input yang diperlukan:
- city_group: Grup kota (lowercase, wajib diisi)
- model_name: Nama model motor (wajib diisi)
- variant_code: Kode varian dari tool get_variants (wajib diisi)
- mode: Mode operasi - get_summary atau get_simulation (opsional)
- include_promo: Apakah mengambil data promo (default: True)
- Parameter range: min_dp, max_dp, min_tenor, max_tenor, min_installment, max_installment (opsional)

Output:
- Simulasi kredit dalam format yang mudah dibaca
- Data promo yang tersedia untuk model dan varian (jika include_promo=True)
- Informasi DP, tenor, cicilan bulanan, dan total pembayaran
- Ringkasan rentang opsi kredit yang tersedia
- Error message jika parameter tidak valid atau tidak ada simulasi yang cocok

Panduan penggunaan parameter:
- Gunakan mode 'get_summary' untuk informasi range (DP paling kecil/besar, tenor min/max)
- Gunakan mode 'get_simulation' untuk simulasi dengan kriteria range tertentu
- Jika mode tidak disediakan, maka akan auto-detect berdasarkan adanya parameter range
- Parameter variant_code harus didapat dari tool get_variants terlebih dahulu
- min_dp harus < max_dp, selisih minimal harus Rp 999999 apapun kondisinya.
- min_tenor harus < max_tenor, selisih minimal harus 11 bulan apapun kondisinya.
- min_installment harus < max_installment, selisih minimal harus Rp 999999 apapun kondisinya.
- Jika user menyebut “di bawah X”, maka gunakan max_only (min=None).
- Jika user menyebut “minimal”, “di atas X”, maka gunakan min_only (max=None).
- Jika user menyebut “X tahun”, terjemahkan ke bulan: X * 12 - 1.
- Jika user menyebut “tenor di bawah X tahun”, maka max_tenor = (X * 12) - 1

Contoh penggunaan parameter:
- min_dp=3000000, max_dp=3999999: DP 3 juta
- min_dp=5000000, max_dp=7999999: DP 5 juta 
- min_dp=10000000, max_dp=14999999: DP 10 juta
- min_dp=15000000, max_dp=19999999: DP 15 juta
- min_dp=20000000, max_dp=24999999: DP 20 juta
- min_tenor=23, max_tenor=34: Tenor 2 tahun
- min_tenor=35, max_tenor=46: Tenor 3 tahun
- min_tenor=47, max_tenor=58: Tenor 4 tahun
- min_tenor=59, max_tenor=70: Tenor 5 tahun
- min_tenor=71, max_tenor=82: Tenor 6 tahun
- min_installment=1000000, max_installment=1999999: Cicilan 1 juta
- min_installment=1500000, max_installment=2499999: Cicilan 1,5 juta
- min_installment=2000000, max_installment=2999999: Cicilan 2 juta
- min_installment=2500000, max_installment=3499999: Cicilan 2,5 juta
- min_installment=3000000, max_installment=3999999: Cicilan 3 juta


CONTOH PERTANYAAN:
- "Berapa cicilan motor BeAT di Jakarta dengan DP 3 juta?"
- "Simulasi kredit Vario 125 tenor 2 tahun di Bandung"
- "Angsuran PCX dengan DP minimal 5 juta"
- "Cicilan motor Scoopy dibawah 1,5 juta per bulan"
- "Range DP dan tenor untuk motor BeAT di Surabaya"
- "Simulasi kredit motor dengan DP 4-6 juta"
- "Berapa cicilan motor dengan tenor dibawah 3 tahun?"
- "DP paling kecil untuk Honda BeAT di Jakarta?"
- "Tenor paling lama berapa tahun untuk motor Honda?"
- "Angsuran 1 jutaan untuk Honda Vario di Medan"
"""
)


class CreditSimulationSchema(BaseModel):
    """
    Schema untuk input parameter tool get_credit_simulation_and_promo.

    Attributes:
        city_group (str): Grup kota (lowercase). HARUS sama dengan yang digunakan di get_variants.
        model_name (str): Nama model. HARUS sama dengan daftar model
        variant_code (str): Kode varian YANG HARUS dari tool get_variants.
        mode (CreditSimulationMode): Mode operasi - get_summary atau get_simulation.
        min_dp (Optional[int]): Minimum Down Payment dalam rupiah.
        max_dp (Optional[int]): Maximum Down Payment dalam rupiah.
        min_tenor (Optional[int]): Minimum tenor kredit dalam bulan.
        max_tenor (Optional[int]): Maximum tenor kredit dalam bulan.
        min_installment (Optional[int]): Minimum cicilan bulanan dalam rupiah.
        max_installment (Optional[int]): Maximum cicilan bulanan dalam rupiah.
        include_promo (Optional[bool]): Apakah mengambil data promo (default: True).
    """

    city_group: str = Field(
        ...,
        description="Grup kota (lowercase). HARUS sama dengan yang digunakan di get_variants.",
        examples=["bandung", "jakarta", "surabaya", "medan"],
    )
    model_name: str = Field(
        ...,
        description="Nama model. HARUS sama dengan daftar model",
        examples=["BeAT", "Vario 125", "PCX", "Scoopy"],
    )
    variant_code: str = Field(
        ...,
        description="Kode varian YANG HARUS dari tool get_variants.",
        examples=["H1B02N41L1A/T", "H1B02N41L1B/T", "H1B02N41L1C/T"],
    )
    mode: Optional[CreditSimulationMode] = Field(
        default=None,
        description="Mode operasi: 'get_summary' untuk informasi range, 'get_simulation' untuk simulasi dengan range. Auto-detect jika tidak diisi.",
    )
    min_dp: Optional[int] = Field(
        default=None,
        description="Minimum Down Payment (DP/Uang Muka) dalam rupiah. Gunakan untuk query 'DP minimal X', 'DP diatas X', 'DP mulai dari X'. Contoh: 'DP 3 jutaan' → min_dp=3000000, max_dp=3999999.",
        examples=[3000000, 5000000, 10000000],
    )
    max_dp: Optional[int] = Field(
        default=None,
        description="Maximum Down Payment (DP/Uang Muka) dalam rupiah. Gunakan untuk query 'DP dibawah X', 'DP maksimal X', 'DP kurang dari X'. Contoh: 'DP dibawah 4 juta' → max_dp=3999999.",
        examples=[3999999, 7999999, 14999999],
    )
    min_tenor: Optional[int] = Field(
        default=None,
        description="Minimum tenor kredit dalam bulan.",
        examples=[12, 24, 36],
    )
    max_tenor: Optional[int] = Field(
        default=None,
        description="Maximum tenor kredit dalam bulan.",
        examples=[23, 35, 47],
    )
    min_installment: Optional[int] = Field(
        default=None,
        description="Minimum cicilan bulanan dalam rupiah.",
        examples=[1000000, 1500000, 2000000],
    )
    max_installment: Optional[int] = Field(
        default=None,
        description="Maximum cicilan bulanan dalam rupiah.",
        examples=[999999, 1499999, 2499999],
    )
    include_promo: Optional[bool] = Field(
        default=True,
        description="Apakah mengambil data promo untuk model dan varian ini. Default: True.",
    )


def _validate_range_parameters(
    dp_min: Optional[int] = None,
    dp_max: Optional[int] = None,
    tenor_min: Optional[int] = None,
    tenor_max: Optional[int] = None,
    installment_min: Optional[int] = None,
    installment_max: Optional[int] = None,
) -> None:
    """
    Validates range parameters according to business rules.

    Rules:
    - DP range must have minimum difference of 999,999
    - Tenor range must have minimum difference of 11 (one year-1)
    - Installment range must have minimum difference of 999,999
    - Min values must always be less than max values

    Raises:
        ToolException: If validation fails
    """
    # Validate DP range
    if dp_min is not None and dp_max is not None:
        if dp_min >= dp_max:
            raise ToolException("Nilai minimum DP harus kurang dari maksimum DP.")
        if dp_max - dp_min < 999999:
            raise ToolException("Rentang DP harus memiliki selisih minimal 999,999.")

    # Validate tenor range
    if tenor_min is not None and tenor_max is not None:
        if tenor_min >= tenor_max:
            raise ToolException("Nilai minimum tenor harus kurang dari maksimum tenor.")
        if tenor_max - tenor_min < 11:
            raise ToolException(
                "Rentang tenor harus memiliki selisih minimal 11 bulan."
            )

    # Validate installment range
    if installment_min is not None and installment_max is not None:
        if installment_min >= installment_max:
            raise ToolException(
                "Nilai minimum installment harus kurang dari maksimum installment."
            )
        if installment_max - installment_min < 999999:
            raise ToolException(
                "Rentang installment harus memiliki selisih minimal 999,999."
            )


def _track_available_options(simulations: list) -> tuple[list, list, list]:
    """
    Track available options from simulations data.
    Returns: (available_tenors, available_down_payment_range, available_installment_range)
    """
    available_tenors = []
    available_down_payment_range = []
    available_installment_range = []

    for sim in simulations:
        if sim.get("tenor") == 0:
            continue
        if sim.get("dp") == 0:
            continue
        if sim.get("installment") == 0:
            continue

        # available tenor
        if sim.get("tenor") not in available_tenors:
            available_tenors.append(sim.get("tenor"))

        # available down payment range
        if not available_down_payment_range:
            available_down_payment_range = [sim.get("dp"), sim.get("dp")]
        elif sim.get("dp") < available_down_payment_range[0]:
            available_down_payment_range[0] = sim.get("dp")
        elif sim.get("dp") > available_down_payment_range[1]:
            available_down_payment_range[1] = sim.get("dp")

        # available installment range
        if not available_installment_range:
            available_installment_range = [
                sim.get("installment"),
                sim.get("installment"),
            ]
        elif sim.get("installment") < available_installment_range[0]:
            available_installment_range[0] = sim.get("installment")
        elif sim.get("installment") > available_installment_range[1]:
            available_installment_range[1] = sim.get("installment")

    return available_tenors, available_down_payment_range, available_installment_range


def _generate_summary_output(
    simulations: list,
    promo_data: Optional[list] = None,
    city_group: Optional[str] = None,
) -> str:
    """
    Generate summary output dengan format:
    Min DP: Rp 2000000
    Max DP: Rp 19000000
    Min Tenor: 11 bulan
    Max Tenor: 34 bulan
    Min Installment: Rp 800000
    Max Installment: Rp 3000000

    Args:
        simulations: Regular credit simulations
        promo_data: Promo data to include in calculations
        city_group: City group for filtering promo data
    """
    # Combine regular simulations with promo credit schemes
    all_simulations = simulations.copy()

    # Add promo credit schemes if available
    if promo_data and city_group:
        promo_simulations = _extract_promo_credit_schemes(promo_data, city_group)
        all_simulations.extend(promo_simulations)

    available_tenors, available_dp_range, available_installment_range = (
        _track_available_options(all_simulations)
    )

    # Sort tenors untuk mendapatkan min/max
    sorted_tenors = sorted(available_tenors)

    summary = f"""
Min DP: Rp {available_dp_range[0]}
Max DP: Rp {available_dp_range[1]}
Min Tenor: {sorted_tenors[0]} bulan
Max Tenor: {sorted_tenors[-1]} bulan
Min Installment: Rp {available_installment_range[0]}
Max Installment: Rp {available_installment_range[1]}"""

    return summary


def _generate_promo_summary(promo_data, city_group: str = None) -> str:
    """
    Generate promo summary output dari data promo dengan informasi detail.

    Args:
        promo_data: Data promo dari API response
        city_group: Grup kota untuk filter promo (case insensitive)

    Returns:
        str: Summary promo dalam format yang mudah dibaca
    """
    if not promo_data:
        return "Tidak ada promo yang tersedia untuk model dan varian ini."

    if isinstance(promo_data, list):
        if len(promo_data) == 0:
            return "Tidak ada promo yang tersedia untuk model dan varian ini."

        # Filter promo berdasarkan city_group jika disediakan
        filtered_promos = []
        if city_group:
            city_group_lower = city_group.lower()
            for promo in promo_data:
                area_list = promo.get("area", [])
                if any(area.lower() == city_group_lower for area in area_list):
                    filtered_promos.append(promo)
        else:
            filtered_promos = promo_data

        if len(filtered_promos) == 0:
            return f"Tidak ada promo yang tersedia untuk area {city_group}."

        promo_summary = f"Ditemukan {len(filtered_promos)} promo tersedia untuk area {city_group}:\n\n"

        for i, promo in enumerate(filtered_promos[:5], 1):  # Limit to 5 promos
            # Caption
            caption = promo.get("caption", "Tidak ada deskripsi")
            if len(caption) > 150:
                caption = caption[:150] + "..."

            promo_summary += f"{i}. {caption}\n"

            # Vehicle information
            vehicle = promo.get("vehicle", {})
            variant_custom = vehicle.get("variant_custom", [])
            if variant_custom:
                promo_summary += "   Kendaraan:\n"
                for variant in variant_custom[:3]:  # Limit to 3 variants
                    variant_name = variant.get("variant_name", "N/A")
                    variant_color = variant.get("variant_color_name", "N/A")
                    promo_summary += f"   - {variant_name} ({variant_color})\n"

            # Credit information
            credit_info = promo.get("credit", [])
            if credit_info:
                promo_summary += "   Paket Kredit:\n"
                for credit in credit_info[:3]:  # Limit to 3 credit options
                    dp_amount = credit.get("dp_amount", 0)
                    discount = promo.get("total_promo_discount", 0)
                    final_dp = dp_amount - discount
                    tenor = credit.get("tenor", [])
                    installment = credit.get("installment_amount", 0)
                    finco_name = credit.get("finco_name", "N/A")

                    tenor_str = ", ".join(map(str, tenor)) if tenor else "N/A"
                    promo_summary += f"   - DP Awal: Rp {dp_amount}, Potongan: Rp {discount}, DP Promo: Rp {final_dp}, Tenor: {tenor_str} bulan, Cicilan: Rp {installment}\n"
                    promo_summary += f"     Leasing: {finco_name}\n"

            # Area information
            area_list = promo.get("area", [])
            if area_list:
                area_str = ", ".join(area_list)
                promo_summary += f"   Area: {area_str}\n"

            promo_summary += "\n"

        if len(filtered_promos) > 5:
            promo_summary += f"... dan {len(filtered_promos) - 5} promo lainnya."

        return promo_summary.strip()

    elif isinstance(promo_data, dict):
        # Handle single promo object
        promo = promo_data

        # Filter berdasarkan city_group jika disediakan
        if city_group:
            area_list = promo.get("area", [])
            city_group_lower = city_group.lower()
            if not any(area.lower() == city_group_lower for area in area_list):
                return f"Promo ini tidak tersedia untuk area {city_group}."

        caption = promo.get("caption", "Tidak ada deskripsi")
        promo_summary = f"1. {caption}\n"

        # Vehicle information
        vehicle = promo.get("vehicle", {})
        variant_custom = vehicle.get("variant_custom", [])
        if variant_custom:
            promo_summary += "   Kendaraan:\n"
            for variant in variant_custom[:3]:
                variant_name = variant.get("variant_name", "N/A")
                variant_color = variant.get("variant_color_name", "N/A")
                promo_summary += f"   - {variant_name} ({variant_color})\n"

        # Credit information
        credit_info = promo.get("credit", [])
        if credit_info:
            promo_summary += "   Paket Kredit:\n"
            for credit in credit_info[:3]:
                dp_amount = credit.get("dp_amount", 0)
                discount = promo.get("total_promo_discount", 0)
                final_dp = dp_amount - discount
                tenor = credit.get("tenor", [])
                installment = credit.get("installment_amount", 0)
                finco_name = credit.get("finco_name", "N/A")

                tenor_str = ", ".join(map(str, tenor)) if tenor else "N/A"
                promo_summary += f"   - DP Awal: Rp {dp_amount}, Potongan: Rp {discount}, DP Promo: Rp {final_dp}, Tenor: {tenor_str} bulan, Cicilan: Rp {installment}\n"
                promo_summary += f"     Leasing: {finco_name}\n"

        # Area information
        area_list = promo.get("area", [])
        if area_list:
            area_str = ", ".join(area_list)
            promo_summary += f"   Area: {area_str}\n"

        return promo_summary

    else:
        return "Format data promo tidak dikenali."


def _extract_promo_credit_schemes(promo_data: list, city_group: str) -> list:
    """Extract credit schemes from promo data and format them as simulation entries."""
    promo_simulations = []

    if not promo_data:
        return promo_simulations

    # Handle both single promo and list of promos
    promos = promo_data if isinstance(promo_data, list) else [promo_data]

    for promo in promos:
        # Filter by city_group (case insensitive) - exact match
        area_list = promo.get("area", [])
        if area_list and city_group:
            city_group_lower = city_group.lower()
            area_match = any(area.lower() == city_group_lower for area in area_list)
            if not area_match:
                continue

        total_discount = promo.get("total_promo_discount", 0)

        # Extract credit information
        credit_info = promo.get("credit", [])
        for credit in credit_info:
            original_dp = credit.get("dp_amount", 0)
            dp_amount = original_dp - total_discount
            tenor_list = credit.get("tenor", [])
            installment = credit.get("installment_amount", 0)

            # Create simulation entries for each tenor
            for tenor in tenor_list:
                if isinstance(tenor, str) and tenor.isdigit():
                    tenor = int(tenor)
                elif isinstance(tenor, str):
                    continue  # Skip non-numeric tenors

                promo_sim = {
                    "dp": dp_amount,
                    "original_dp": original_dp,
                    "tenor": tenor,
                    "installment": installment,
                    "is_promo": True,
                }
                promo_simulations.append(promo_sim)

    return promo_simulations


def _filter_simulations(
    simulations: list,
    promo_data: Optional[list] = None,
    city_group: Optional[str] = None,
    dp_min: Optional[int] = None,
    dp_max: Optional[int] = None,
    tenor_min: Optional[int] = None,
    tenor_max: Optional[int] = None,
    installment_min: Optional[int] = None,
    installment_max: Optional[int] = None,
) -> str:
    # Add is_promo flag to regular simulations
    regular_simulations = []
    for sim in simulations:
        sim_with_flag = sim.copy()
        sim_with_flag["is_promo"] = False
        # Add original_dp same as dp for regular simulations
        sim_with_flag["original_dp"] = sim_with_flag["dp"]
        regular_simulations.append(sim_with_flag)

    # Extract and add promo credit schemes
    all_simulations = regular_simulations.copy()
    if promo_data and city_group:
        promo_simulations = _extract_promo_credit_schemes(promo_data, city_group)
        all_simulations.extend(promo_simulations)

    # Apply filters
    results = []
    for sim in all_simulations:
        # Handle DP filtering
        if dp_min is not None and sim.get("dp") < dp_min:
            continue
        if dp_max is not None and sim.get("dp") > dp_max:
            continue

        # Handle tenor filtering
        if tenor_min is not None and sim.get("tenor") < tenor_min:
            continue
        if tenor_max is not None and sim.get("tenor") > tenor_max:
            continue

        # Handle installment filtering
        if installment_min is not None and sim.get("installment") < installment_min:
            continue
        if installment_max is not None and sim.get("installment") > installment_max:
            continue

        results.append(sim)

    if not results:
        raise NotAvailableCreditScheme()

    csv_formatted = "original_dp,dp,tenor,installment,is_promo\n"
    for res in results:
        is_promo_flag = "true" if res.get("is_promo", False) else "false"
        csv_formatted += f"{res['original_dp']},{res['dp']},{res['tenor']},{res['installment']},{is_promo_flag}\n"
    return csv_formatted


async def _aget_credit_simulation_tool(
    city_group: str,
    model_name: str,
    variant_code: str,
    mode: Optional[CreditSimulationMode] = None,
    min_dp: Optional[int] = None,
    max_dp: Optional[int] = None,
    min_tenor: Optional[int] = None,
    max_tenor: Optional[int] = None,
    min_installment: Optional[int] = None,
    max_installment: Optional[int] = None,
    include_promo: Optional[bool] = True,
    config: RunnableConfig = None,
) -> str:
    # Auto-detect mode untuk backward compatibility
    if mode is None:
        if any(
            [min_dp, max_dp, min_tenor, max_tenor, min_installment, max_installment]
        ):
            mode = CreditSimulationMode.GET_SIMULATION
        else:
            mode = CreditSimulationMode.GET_SUMMARY

    # Validasi berdasarkan mode
    if mode == CreditSimulationMode.GET_SIMULATION:
        if not any(
            [min_dp, max_dp, min_tenor, max_tenor, min_installment, max_installment]
        ):
            raise ToolException(
                "Mode 'get_simulation' memerlukan minimal salah satu dari parameter range (min_dp, max_dp, min_tenor, max_tenor, min_installment, max_installment)."
            )

    promo_data = None
    try:
        if not city_group:
            raise ToolException(
                "City group diperlukan untuk mengambil data simulasi kredit."
            )

        organization = config.get("configurable", {}).get("organization", "amarta")

        # Ambil data simulasi (sama untuk kedua mode)
        service = CreditSimulationService()
        simulations = await service.aget_credit_simulation(
            model_name, variant_code, city_group
        )
        simulations = simulations["data"]["formatted"]

        # Ambil data promo jika diminta
        if include_promo:
            try:
                promo_service = GetPromoService()
                promo_response = await promo_service.aget_promo_by_model_and_variant(
                    organization, model_name, variant_code
                )
                promo_data = promo_response.get("data", [])
            except Exception as e:
                logging.warning(f"Gagal mengambil data promo: {e}")
                promo_data = None

        # Routing berdasarkan mode
        if mode == CreditSimulationMode.GET_SUMMARY:
            credit_summary = _generate_summary_output(
                simulations, promo_data, city_group
            )
            if promo_data:
                promo_summary = _generate_promo_summary(promo_data, city_group)
                return f"{credit_summary}\n\n=== PROMO TERSEDIA ===\n{promo_summary}"
            return credit_summary
        else:  # GET_SIMULATION
            credit_result = _filter_simulations(
                simulations,
                promo_data=promo_data,
                city_group=city_group,
                dp_min=min_dp,
                dp_max=max_dp,
                tenor_min=min_tenor,
                tenor_max=max_tenor,
                installment_min=min_installment,
                installment_max=max_installment,
            )
            if promo_data:
                promo_summary = _generate_promo_summary(promo_data, city_group)
                return f"{credit_result}\n\n=== DETAIL PROMO ===\n{promo_summary}"
            return credit_result
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            raise ToolException(
                "Tidak ada simulasi kredit yang ditemukan untuk variant di area ini."
            )
    except NotAvailableCreditScheme:
        # Track available options for better error messages
        available_tenors, available_down_payment_range, available_installment_range = (
            _track_available_options(simulations)
        )
        error_message = dedent(
            f"""
            Skema kredit tidak tersedia untuk kriteria yang diminta.
            Skema kredit yang tersedia:    
            - Min DP: Rp {available_down_payment_range[0]} 
            - Max DP: Rp {available_down_payment_range[1]} 
            - Tenor: {", ".join(map(str, available_tenors))}
            - Min Installment: Rp {available_installment_range[0]} 
            - Max Installment: Rp {available_installment_range[1]} 
        """
        )
        if promo_data:
            promo_summary = _generate_promo_summary(promo_data, city_group)
            error_message += f"\n\n=== PROMO TERSEDIA ===\n{promo_summary}"
        raise ToolException(error_message)
    except Exception as e:
        logging.error(e)
        raise ToolException(f"Terjadi kesalahan saat mengambil data simulasi: {e}")


get_credit_simulation_and_promo_tool = StructuredTool.from_function(
    name="get_credit_simulation_and_promo",
    coroutine=_aget_credit_simulation_tool,
    description=_DESCRIPTION,
    args_schema=CreditSimulationSchema,
)

if __name__ == "__main__":
    import asyncio

    async def main():
        # Test with wider range to show both regular and promo simulations
        result = await get_credit_simulation_and_promo_tool.ainvoke(
            {
                "city_group": "BEKASI",
                "model_name": "BEAT DLX",
                "max_dp": 199999.0,
                "variant_code": "H1B02N42L1AA/T",
                "min_dp": 100000.0,
                "config": {"configurable": {"organization": "amarta"}},
            }
        )
        print(result)

    asyncio.run(main())
