import textwrap
from typing import List, Dict, Any, Optional
import aiohttp
import asyncio
import json

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import StructuredTool, ToolException
from pydantic import BaseModel, Field

from app.services.catalogue_service import CatalogueService

_DESCRIPTION = textwrap.dedent(
    """
Mendapatkan detail varian motor/mobil berdasarkan model dan kota.

Tool ini digunakan untuk:
- Mendapatkan daftar varian motor/mobil yang tersedia untuk model tertentu
- Menampilkan informasi harga otr, kode varian, dan nama varian
- Melihat link produk untuk setiap varian motor/mobil
- Memfilter varian berdasarkan lokasi/kota tertentu
- Mendapatkan data lengkap varian untuk proses simulasi kredit

Input yang diperlukan:
- model_names: Nama model motor/mobil yang valid dari VARIABEL SISTEM (array string, bisa lebih dari satu).
- city_group: Grup kota dalam format lowercase (string, wajib diisi)
- include_specs: Boolean untuk menyertakan spesifikasi teknis lengkap (default: false)

Kapan menggunakan include_specs=true:
- Ketika user menanyakan spesifikasi teknis detail (mesin, dimensi, fitur, dll)
- Ketika user ingin membandingkan spesifikasi antar varian
- Ketika user menanyakan detail teknis seperti "spek lengkap", "fitur apa saja", "ukuran mesin"
- Ketika user membutuhkan informasi teknis untuk pengambilan keputusan

Kapan menggunakan include_specs=false (default):
- Ketika user hanya menanyakan harga, nama varian, atau kode varian
- Ketika user ingin daftar varian sederhana tanpa detail teknis
- Untuk pertanyaan umum tentang ketersediaan varian

Output:
- Data varian dalam format daftar vertikal yang mudah dibaca
- Setiap varian ditampilkan dengan nomor urut dan informasi lengkap
- Informasi meliputi: nama varian, model, kode varian, harga OTR, dan link produk
- Harga ditampilkan dengan format mata uang Rupiah dan pemisah ribuan
- Error message jika model tidak ditemukan atau tidak ada varian tersedia
- Link produk yang dapat diakses untuk melihat detail lebih lanjut

CONTOH PERTANYAAN (include_specs=false):
- "Varian motor BeAT di Jakarta"
- "Pilihan warna motor Vario 125 di Bandung"
- "Tipe motor PCX yang tersedia di Surabaya"
- "Harga Honda PCX 160 di Surabaya berapa?"
- "Kode varian Honda Scoopy di Medan"
- "Apa saja pilihan Honda ADV 150 di Bandung?"

CONTOH PERTANYAAN (include_specs=true):
- "Spesifikasi lengkap Honda BeAT di Jakarta"
- "Detail teknis Honda Vario 125 di Bandung"
- "Fitur apa saja yang ada di Honda PCX 160?"
- "Ukuran mesin Honda CB150R berapa cc?"
- "Dimensi dan berat Honda ADV 150"
- "Perbandingan spek Honda Scoopy vs BeAT"
- "Kapasitas tangki bensin Honda PCX 160"
- "Sistem rem Honda Vario 125 apa?"
"""
)


class VariantSchema(BaseModel):
    """
    Schema untuk input parameter tool get_variants.

    Attributes:
        model_names (List[str]): Nama model motor yang valid
        city_group (str): Grup kota dalam format lowercase untuk lokasi spesifik
        include_specs (bool): Apakah menyertakan spesifikasi teknis dari URL JSON (default: False)
    """

    model_names: List[str] = Field(
        ...,
        description="Nama model yang tersedia pada daftar Model",
    )
    city_group: str = Field(
        description="Grup kota dalam format lowercase untuk menentukan lokasi dan harga spesifik.",
    )
    include_specs: bool = Field(
        default=False,
        description="Apakah menyertakan spesifikasi teknis dari URL JSON. Set True jika ingin mendapatkan detail spesifikasi lengkap kendaraan.",
    )


def _filter_variants(variants: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Filter dan validasi data varian dari API response.

    Args:
        variants (List[Dict[str, Any]]): List data varian dari API

    Returns:
        List[Dict[str, Any]]: Data varian yang sudah difilter dan divalidasi

    Note:
        Saat ini fungsi ini mengembalikan semua varian tanpa filter,
        tapi bisa dikembangkan untuk filtering berdasarkan kriteria tertentu.
    """
    if not variants:
        return []

    # Validasi struktur data varian
    valid_variants = []
    for variant in variants:
        if all(
            key in variant
            for key in ["variant_code", "variant_name", "model_name", "price"]
        ):
            valid_variants.append(variant)

    return valid_variants


async def _fetch_specifications(url: str) -> Optional[Dict[str, Any]]:
    """
    Fetch spesifikasi kendaraan dari URL JSON secara asinkron.

    Args:
        url (str): URL ke file JSON yang berisi spesifikasi kendaraan

    Returns:
        Optional[Dict[str, Any]]: Data spesifikasi dalam format dictionary, atau None jika gagal

    Note:
        Fungsi ini menggunakan aiohttp untuk melakukan HTTP request asinkron.
        Jika terjadi error (network, parsing JSON, dll), akan mengembalikan None.
    """
    if not url or not url.strip():
        return None

    try:
        timeout = aiohttp.ClientTimeout(total=10)  # 10 detik timeout
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url.strip()) as response:
                if response.status == 200:
                    content_type = response.headers.get("content-type", "")
                    if "application/json" in content_type or url.endswith(".json"):
                        specs_data = await response.json()
                        return specs_data
                    else:
                        # Coba parse sebagai JSON meskipun content-type tidak sesuai
                        text_content = await response.text()
                        return json.loads(text_content)
                else:
                    print(
                        f"Warning: Failed to fetch specifications from {url}, status: {response.status}"
                    )
                    return None
    except asyncio.TimeoutError:
        print(f"Warning: Timeout when fetching specifications from {url}")
        return None
    except aiohttp.ClientError as e:
        print(
            f"Warning: Network error when fetching specifications from {url}: {str(e)}"
        )
        return None
    except json.JSONDecodeError as e:
        print(
            f"Warning: JSON decode error when parsing specifications from {url}: {str(e)}"
        )
        return None
    except Exception as e:
        print(
            f"Warning: Unexpected error when fetching specifications from {url}: {str(e)}"
        )
        return None


def _format_variant_output(
    variants: List[Dict[str, Any]],
    city_group: str,
    organization: str,
    include_specs: bool = False,
    specifications: Optional[Dict[str, Dict[str, Any]]] = None,
) -> str:
    """
    Format data varian menjadi daftar vertikal dengan informasi lengkap.

    Args:
        variants (List[Dict[str, Any]]): Data varian yang sudah difilter
        city_group (str): Grup kota untuk membuat product link
        organization (str): Organisasi untuk menentukan format link
        include_specs (bool): Apakah menyertakan spesifikasi teknis
        specifications (Optional[Dict[str, Dict[str, Any]]]): Data spesifikasi per variant_code

    Returns:
        str: Data varian dalam format daftar vertikal
    """
    if not variants:
        return "Tidak ada varian yang tersedia untuk model ini."

    result = []

    for i, variant in enumerate(variants, 1):
        variant_code = variant.get("variant_code", "")
        variant_name = variant.get("variant_name", "")
        model_name = variant.get("model_name", "")
        price = variant.get("price", 0)
        description = variant.get("ai_notes", "")

        # Spesifikasi teknis dalam format file JSON
        specs_in_json_file = variant.get("url_vehicle_specification", "")

        # Remove slashes from variant_code to create a valid URL path segment
        variant_code_for_url = variant_code.replace("/", "")

        product_link = ""
        if organization == "amarta":
            product_link = f"https://amartahonda.com/baru/{city_group.lower()}/{variant_code_for_url}"
        elif organization == "vinfast":
            product_link = f"https://amartavinfast.com/variant/{variant_code_for_url}"

        # Format harga dengan pemisah ribuan
        formatted_price = f"Rp {price:,}".replace(",", ".")

        # Base variant info
        variant_info = f"""
{i}. {variant_name}
   • Model: {model_name}
   • Kode Varian: {variant_code}
   • Harga OTR: {formatted_price}
   • Link Produk: {product_link}
   • Deskripsi: {description}"""

        # Tambahkan spesifikasi jika diminta dan tersedia
        if include_specs and specifications and variant_code in specifications:
            specs = specifications[variant_code]
            variant_info += "\n   • Spesifikasi Teknis:"

            # Format spesifikasi dengan indentasi yang rapi
            for spec_key, spec_value in specs.items():
                if spec_value:  # Hanya tampilkan jika ada nilai
                    # Format key menjadi lebih readable
                    formatted_key = spec_key.replace("_", " ").title()
                    if isinstance(spec_value, dict):
                        variant_info += f"\n     - {formatted_key}:"
                        for sub_key, sub_value in spec_value.items():
                            if sub_value:
                                formatted_sub_key = sub_key.replace("_", " ").title()
                                variant_info += (
                                    f"\n       * {formatted_sub_key}: {sub_value}"
                                )
                    elif isinstance(spec_value, list):
                        variant_info += f"\n     - {formatted_key}: {', '.join(map(str, spec_value))}"
                    else:
                        variant_info += f"\n     - {formatted_key}: {spec_value}"
        elif include_specs and specs_in_json_file:
            variant_info += f"\n   • URL Spesifikasi: {specs_in_json_file}"

        result.append(variant_info.strip())

    return "\n\n".join(result)


async def _aget_variants_tool(
    model_names: List[str],
    city_group: str = "bandung",
    include_specs: bool = False,
    config: RunnableConfig = None,
) -> str:
    """
    Fungsi asinkron untuk mengambil data varian motor.

    Args:
        model_names (List[str]): Nama model motor yang valid
        city_group (str): Grup kota dalam format lowercase
        include_specs (bool): Apakah menyertakan spesifikasi teknis dari URL JSON

    Returns:
        str: Data varian dalam format daftar vertikal yang mudah dibaca

    Raises:
        ToolException: Jika terjadi error saat mengambil data
    """
    try:
        # Validasi input
        if not model_names:
            raise ToolException("model_names tidak boleh kosong")

        if not city_group or not city_group.strip():
            city_group = "bandung"  # Default fallback

        # Pastikan city_group lowercase
        city_group = city_group.lower().strip()
        model_names = [model.strip() for model in model_names]

        organization = config.get("configurable", {}).get("organization", "amarta")
        service = CatalogueService(organization=organization)
        variants_response = await service.aget_variants(
            city_group=city_group,
            model_names=model_names,
        )

        if not variants_response:
            raise ToolException("Response API kosong atau tidak valid")

        variants_data = variants_response.get("data", [])
        if not variants_data:
            return f"Tidak ada varian tersedia untuk model {', '.join(model_names)} di area '{city_group}'"

        filtered_variants = _filter_variants(variants_data)

        # Fetch spesifikasi jika diminta
        specifications = {}
        if include_specs and filtered_variants:
            # Kumpulkan semua URL spesifikasi yang unik
            spec_urls = {}
            for variant in filtered_variants:
                specs_url = variant.get("url_vehicle_specification", "")
                if specs_url and specs_url.strip():
                    variant_code = variant.get("variant_code", "")
                    spec_urls[variant_code] = specs_url.strip()

            # Fetch spesifikasi secara paralel untuk performa yang lebih baik
            if spec_urls:
                fetch_tasks = []
                for variant_code, url in spec_urls.items():
                    fetch_tasks.append(_fetch_specifications(url))

                # Tunggu semua fetch selesai
                spec_results = await asyncio.gather(
                    *fetch_tasks, return_exceptions=True
                )

                # Map hasil ke variant_code
                for i, (variant_code, url) in enumerate(spec_urls.items()):
                    if i < len(spec_results) and not isinstance(
                        spec_results[i], Exception
                    ):
                        if spec_results[i] is not None:
                            specifications[variant_code] = spec_results[i]

        return _format_variant_output(
            filtered_variants,
            city_group,
            organization,
            include_specs=include_specs,
            specifications=specifications,
        )

    except ToolException:
        raise
    except Exception as e:
        error_msg = (
            f"Gagal mengambil data varian untuk model {', '.join(model_names)}: {str(e)}. "
            "Pastikan: "
            "1. model_names valid (contoh: ['BeAT', 'Vario 125', 'PCX 160']), "
            "2. city_group dalam format lowercase (contoh: 'bandung'), "
            "3. Koneksi internet stabil"
        )
        raise ToolException(error_msg)


get_variants_tool = StructuredTool.from_function(
    name="get_variants",
    coroutine=_aget_variants_tool,
    args_schema=VariantSchema,
    description=_DESCRIPTION,
    return_direct=False,
)

if __name__ == "__main__":
    import asyncio

    config = RunnableConfig(configurable={"organization": "amarta"})

    async def main():
        result = await get_variants_tool.ainvoke(
            {
                "model_names": ["BeAT DLX"],
                "city_group": "bandung",
                "include_specs": True,
            },
            config,
        )
        print(result)

    asyncio.run(main())
