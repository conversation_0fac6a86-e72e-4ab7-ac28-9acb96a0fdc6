import logging
import textwrap
from typing import Dict, Any, Optional

import httpx
from langchain_core.tools import StructuredTool, ToolException
from pydantic import BaseModel, Field

from app.services.get_promo_service import (
    GetPromoService,
)

_DESCRIPTION = textwrap.dedent(
    """
Mengambil informasi iklan berdasarkan ID iklan atau kode promo.

Tool ini digunakan untuk:
- Mendapatkan informasi iklan berdasarkan source_id (hanya angka)
- Mendapatkan informasi iklan berdasarkan promo_code (kombinasi angka dan huruf)

Input yang diperlukan:
- source_id: ID iklan untuk mengambil data iklan (string, hanya angka, opsional jika promo_code diisi)
- promo_code: Kode promo untuk mengambil data iklan (string, kombinasi angka dan huruf, opsional jika source_id diisi)

Output:
- Informasi iklan dalam format yang mudah dibaca
- Data lengkap promo dalam format yang mudah dibaca
- Informasi kendaraan, kredit, area, dan periode berlaku

CONTOH POLA PERTANYAAN:
"Saya tertarik dengan iklan ini.
ID:[ID IKLAN berupa angka]
Headline:[Headline Iklan]
Body:[Deskripsi Iklan. Di dalamnya ada pattern "kode:<KODE PROMO>"]
[Text pesan Customer]"
"""
)


class GetAdInformationParams(BaseModel):
    """
    Schema untuk input parameter tool get_ad_information.

    Attributes:
        source_id (str, optional): ID iklan yang ingin dilihat (hanya angka).
        promo_code (str, optional): Kode promo yang ingin dilihat (kombinasi angka dan huruf).
    """

    source_id: Optional[str] = Field(
        None,
        description="ID iklan yang ingin dilihat (hanya angka).",
        min_length=1,
        max_length=50,
        examples=["6730637160071", "1234567890123"],
        pattern=r"^\d+$",  # Hanya angka
    )

    promo_code: Optional[str] = Field(
        None,
        description="Kode promo yang ingin dilihat (kombinasi angka dan huruf).",
        min_length=1,
        max_length=50,
        examples=["DEALBEKASIBEAT800", "SCOOPYDP800CRB"],
    )


def _format_ad_output(ad_data: Dict[str, Any]) -> str:
    """
    Format output data iklan menjadi string yang mudah dibaca.
    Args:
        ad_data: Data iklan dari API response
    Returns:
        str: Formatted string dengan informasi iklan
    """
    if not ad_data.get("success", False):
        return "Data iklan tidak tersedia atau tidak valid."
    data = ad_data.get("data", {})
    company = data.get("company", "amarta").lower()
    output = f"Judul: {data.get('caption', 'N/A')}\n\n"
    output += f"Metode Pembelian: {data.get('purchase_method', 'N/A')}\n"
    output += f"Nilai Potongan (Diskon): Rp {data.get('total_promo_discount', 0)}\n"
    # Area info
    areas = data.get("area", [])
    if areas:
        output += f"Area: {', '.join(areas).title()}\n"
        default_address = data.get("default_address", {})
        if default_address:
            output += f"Alamat Default: {default_address.get('city_name', '').title()}, {default_address.get('province_name', '').title()}\n"
    # Promo codes
    promo_codes = data.get("promo_codes", [])
    if promo_codes:
        output += f"Kode Promo: {', '.join(promo_codes)}\n"
    # Vehicle info
    vehicle = data.get("vehicle", {})
    if vehicle:
        output += f"\nModel: {vehicle.get('model_name', 'N/A')}\n"
        # Variant info
        variants = vehicle.get("variant_custom", [])
        if variants:
            output += f"Varian Tersedia:\n"
            for variant in variants:
                variant_name = variant.get("variant_name", "N/A")
                variant_code = variant.get("variant_code", "N/A")
                variant_color_name = variant.get("variant_color_name", "N/A")
                variant_code_for_url = variant_code.replace("/", "")
                product_link = f"https://amartahonda.com/baru/{areas[0]}/{variant_code_for_url}"  # Asumsikan ada 'product_link'

                if company == "vinfast":
                    product_link = (
                        f"https://amartavinfast.com/variant/{variant_code_for_url}"
                    )

                output += f"  - Variant: {variant_name}\n"
                output += f"    Kode: {variant_code}\n"
                output += f"    Warna: {variant_color_name}\n"
                output += f"    Product link: {product_link}\n"
    # Credit info
    credits = data.get("credit", [])
    if credits:
        output += f"\nOpsi Kredit:\n"
        for i, credit in enumerate(credits, 1):
            dp_discount = credit.get("dp_amount", 0) - data.get(
                "total_promo_discount", 0
            )
            output += f"Opsi {i}:\n"
            output += f"  DP: Rp {credit.get('dp_amount', 0)} - Rp {data.get('total_promo_discount', 0)} = Rp {dp_discount} (sudah dikurangi diskon)\n"
            output += f"  Tenor: {', '.join(credit.get('tenor', []))} bulan\n"
            output += f"  Cicilan: Rp {credit.get('installment_amount', 0)} per bulan\n"
            output += f"  Leasing: {credit.get('finco_name', 'N/A')}\n"
    return output.strip()


async def get_ad_from_ad_tool(
    source_id: Optional[str] = None, promo_code: Optional[str] = None
) -> str:
    """
    Mengambil data iklan secara asynchronous berdasarkan ID iklan atau kode promo.

    Args:
        source_id: ID iklan untuk mengambil data iklan (opsional, hanya angka)
        promo_code: Kode promo untuk mengambil data iklan (opsional, kombinasi angka dan huruf)

    Returns:
        str: Formatted string dengan informasi iklan

    Raises:
        ToolException: Jika terjadi error dalam pengambilan data

    Strategi:
        - Prioritas pertama cek berdasarkan source_id jika tersedia
        - Jika source_id tidak tersedia atau tidak ditemukan, maka cek berdasarkan promo_code
    """
    try:
        # Validasi input
        if not source_id and not promo_code:
            raise ToolException(
                "Harus menyediakan salah satu: source_id atau promo_code."
            )

        # Get promo data
        service = GetPromoService()
        promo_data = None

        # Strategi: Prioritas source_id dulu jika tersedia
        if source_id:
            try:
                clean_source_id = source_id.strip()
                if clean_source_id:
                    promo_data = await service.aget_promo_from_ad_source_id(
                        clean_source_id
                    )
                    # Jika berhasil dan ada data, kembalikan hasilnya
                    if promo_data and promo_data.get("success"):
                        return _format_ad_output(promo_data)
            except Exception as e:
                # Jika source_id gagal, lanjutkan ke promo_code (jika tersedia)
                logging.warning(f"Source ID {source_id} tidak ditemukan: {e}")
                pass

        # Jika source_id tidak ada atau tidak ditemukan, cek promo_code
        if promo_code and not (promo_data and promo_data.get("success")):
            try:
                clean_promo_code = promo_code.strip()
                if clean_promo_code:
                    promo_data = await service.aget_promo_by_code(clean_promo_code)
            except Exception as e:
                logging.error(f"Error saat mencari dengan promo code {promo_code}: {e}")
                raise ToolException(
                    f"Kode promo tidak ditemukan. Error: {str(e)}. "
                    "Disarankan untuk menghubungi admin."
                )

        # Jika tidak ada data yang ditemukan
        if not promo_data or not promo_data.get("success"):
            raise ToolException(
                "Data iklan tidak ditemukan untuk source_id atau promo_code yang diberikan."
            )

        # Format and return output
        return _format_ad_output(promo_data)

    except httpx.HTTPStatusError as e:
        raise ToolException(
            f"Iklan tidak ditemukan. Error: {str(e)}. "
            "Disarankan untuk menghubungi admin."
        )
    except ValueError as ve:
        raise ToolException(f"Input tidak valid: {str(ve)}")
    except Exception as e:
        logging.error(f"Error in get_ad_information_tool: {e}")
        raise ToolException(
            f"Terjadi kesalahan saat mengambil data iklan: {str(e)}. "
            "Disarankan untuk menghubungi admin."
        )


get_ad_information_tool = StructuredTool.from_function(
    name="get_ad_information",
    coroutine=get_ad_from_ad_tool,
    description=_DESCRIPTION,
    args_schema=GetAdInformationParams,
)

if __name__ == "__main__":
    import asyncio

    async def main():
        # result = await get_ad_information_tool.ainvoke(
        #     {"source_id": "6730637160071"}
        # )
        # print("Result with source_id:")
        # print(result)
        #
        # print("\n" + "=" * 50 + "\n")

        result = await get_ad_information_tool.ainvoke(
            {"promo_code": "DEALBEKASIBEAT800"}
        )
        print("Result with promo_code:")
        print(result)

    asyncio.run(main())
