import asyncio
import textwrap
from typing import Dict, Any

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import StructuredTool, ToolException
from pydantic import BaseModel, Field

from app.services.dealer_service import DealerService

_DESCRIPTION = textwrap.dedent(
    """
    Tool untuk mendapatkan informasi dealer berdasarkan organisasi yang dikonfigurasi.
    
    Tool ini digunakan untuk:
    - Mengambil data dealer termasuk informasi kontak, alamat, dan lokasi
    - Menampilkan informasi cabang dealer yang lengkap
    - Memberikan detail jam operasional, nomor telepon, dan media sosial
    - Menyediakan koordinat Google Maps untuk navigasi
    
    Output:
    - Informasi dealer terformat dengan alamat lengkap
    - Jam operasional dalam format terstruktur
    - Nomor telepon dalam format internasional
    - Link media sosial resmi
    - Koordinat Google Maps sebagai link yang dapat diklik
    
    CONTOH PERTANYAAN:
    - "Di mana saya bisa menemukan dealer?"
    - "Tunjukkan lokasi dealer"
    - "Apa detail kontak untuk dealer?"
    - "Saya butuh informasi dealer"
    - "Jam buka dealer"
    - "Alamat lengkap dealer"
    - "Nomor telepon dealer terdekat"
    """
)





async def _aget_dealer_data_tool(config: RunnableConfig = None) -> str:
    """
    Get dealer data for the configured organization.

    Args:
        config: RunnableConfig containing organization information

    Returns:
        Formatted dealer data information

    Raises:
        ToolException: If organization is invalid or API call fails
    """
    try:
        # Get organization from config
        organization = config.get("configurable", {}).get("organization", "amarta")
        
        # Validate organization parameter
        if organization not in ["vinfast", "amarta"]:
            raise ToolException(
                f"Invalid organization '{organization}'. Must be 'vinfast' or 'amarta'."
            )

        # Initialize dealer service and get data
        dealer_service = DealerService()
        dealer_data = await dealer_service.aget_dealer_data(organization)

        # Format the response for user-friendly output
        formatted_response = _format_dealer_data_output(dealer_data, organization)

        return formatted_response

    except ToolException:
        raise
    except Exception as e:
        raise ToolException(f"Failed to get dealer data: {str(e)}")


def _format_dealer_data_output(dealer_data: Dict[str, Any], organization: str) -> str:
    """
    Format dealer data for comprehensive display with complete branch information.

    Args:
        dealer_data: Raw dealer data from API
        organization: Organization name

    Returns:
        Formatted string with complete dealer information
    """
    if not dealer_data:
        return f"No dealer data found for {organization}."

    formatted_output = f"DEALER DATA - {organization.upper()}\n\n"

    # Handle different response structures
    if isinstance(dealer_data, dict) and "data" in dealer_data:
        data = dealer_data["data"]

        if isinstance(data, dict):
            # Organization-level data with addresses array
            if "addresses" in data and isinstance(data["addresses"], list):
                for i, branch in enumerate(data["addresses"], 1):
                    formatted_output += f"CABANG {i}: {branch.get('name', 'N/A')}\n"

                    # Alamat lengkap cabang
                    if branch.get("address"):
                        formatted_output += f"- Alamat: {branch['address']}\n"

                    # Jam operasional
                    if branch.get("operational_hours"):
                        op_hours = branch["operational_hours"]
                        formatted_output += "- Jam operasional:\n"
                        day_names = {
                            "mon": "Senin",
                            "tue": "Selasa",
                            "wed": "Rabu",
                            "thu": "Kamis",
                            "fri": "Jumat",
                            "sat": "Sabtu",
                            "sun": "Minggu",
                        }
                        for day, hours in op_hours.items():
                            if (
                                isinstance(hours, dict)
                                and "open" in hours
                                and "close" in hours
                            ):
                                day_name = day_names.get(day, day.capitalize())
                                formatted_output += f"  {day_name}: {hours['open']} - {hours['close']}\n"

                    # Nomor telepon
                    if branch.get("whatsapp"):
                        phone = branch["whatsapp"]
                        if not phone.startswith("+"):
                            phone = f"+{phone}"
                        formatted_output += f"- Telepon: {phone}\n"
                    elif branch.get("phone_number"):
                        phone = branch["phone_number"]
                        if not phone.startswith("+"):
                            phone = f"+{phone}"
                        formatted_output += f"- Telepon: {phone}\n"

                    # Link sosial media (dari data organisasi - konsisten untuk semua cabang)
                    if data.get("social_media"):
                        social_media = data["social_media"]
                        if isinstance(social_media, dict):
                            formatted_output += "- Media sosial:\n"
                            for platform, link in social_media.items():
                                if link:
                                    formatted_output += (
                                        f"  {platform.capitalize()}: {link}\n"
                                    )

                    # Koordinat Google Maps
                    if branch.get("coordinate"):
                        coord = branch["coordinate"]
                        if coord.get("lat") and coord.get("lng"):
                            lat = coord["lat"]
                            lng = coord["lng"]
                            maps_link = f"https://maps.google.com/?q={lat},{lng}"
                            formatted_output += f"- Google Maps: {maps_link}\n"

                    formatted_output += "\n"
            else:
                # Single dealer without addresses array
                formatted_output += f"CABANG: {data.get('name', 'N/A')}\n"

                if data.get("address"):
                    if isinstance(data["address"], list):
                        formatted_output += f"- Alamat: {', '.join(data['address'])}\n"
                    else:
                        formatted_output += f"- Alamat: {data['address']}\n"

                if data.get("phone_number"):
                    phone = data["phone_number"]
                    if not phone.startswith("+"):
                        phone = f"+{phone}"
                    formatted_output += f"- Telepon: {phone}\n"

                # Link sosial media (dari data organisasi)
                if data.get("social_media"):
                    social_media = data["social_media"]
                    if isinstance(social_media, dict):
                        formatted_output += "- Media sosial:\n"
                        for platform, link in social_media.items():
                            if link:
                                formatted_output += (
                                    f"  {platform.capitalize()}: {link}\n"
                                )

        elif isinstance(data, list) and data:
            # Array of dealers - ambil social media dari item pertama jika ada
            org_social_media = None
            if data and isinstance(data[0], dict) and data[0].get("social_media"):
                org_social_media = data[0]["social_media"]

            for i, dealer in enumerate(data, 1):
                formatted_output += f"CABANG {i}: {dealer.get('name', 'N/A')}\n"

                if dealer.get("address"):
                    formatted_output += f"- Alamat: {dealer['address']}\n"

                if dealer.get("phone"):
                    phone = dealer["phone"]
                    if not phone.startswith("+"):
                        phone = f"+{phone}"
                    formatted_output += f"- Telepon: {phone}\n"

                # Link sosial media (konsisten untuk semua cabang)
                if org_social_media and isinstance(org_social_media, dict):
                    formatted_output += "- Media sosial:\n"
                    for platform, link in org_social_media.items():
                        if link:
                            formatted_output += f"  {platform.capitalize()}: {link}\n"

                formatted_output += "\n"

    return formatted_output


# Create the structured tool
get_dealer_data_tool = StructuredTool.from_function(
    coroutine=_aget_dealer_data_tool,
    name="get_dealer_data",
    description=_DESCRIPTION,
    return_direct=False,
)


if __name__ == "__main__":

    async def test_get_dealer_data_tool():
        """Test function for get dealer data tool."""
        print("Testing get_dealer_data_tool...\n")

        # Test cases
        test_cases = [
            {"configurable": {"organization": "vinfast"}},
            {"configurable": {"organization": "amarta"}},
            {"configurable": {"organization": "invalid_org"}},  # This should raise an exception
        ]

        for config in test_cases:
            organization = config["configurable"]["organization"]
            print(f"Testing with organization: {organization}")
            try:
                result = await _aget_dealer_data_tool(config)
                print(f"Result: {result}")
            except ToolException as e:
                print(f"ToolException: {e}")
            except Exception as e:
                print(f"Unexpected error: {e}")
            print("-" * 50)

    # Run the test
    asyncio.run(test_get_dealer_data_tool())
