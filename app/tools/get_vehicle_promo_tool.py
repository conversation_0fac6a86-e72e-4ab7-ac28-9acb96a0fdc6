import textwrap
from typing import Optional, Dict, Any

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import StructuredTool, ToolException
from pydantic import BaseModel, Field

from app.services.get_promo_service import GetPromoService

_DESCRIPTION = textwrap.dedent(
    """
Mendapatkan informasi promo kendaraan berdasarkan model dan varian.

Tool ini digunakan untuk:
- Mendapatkan data promo yang tersedia untuk model kendaraan tertentu
- Menampilkan detail promo berdasarkan model dan kode varian (opsional)
- Melihat informasi lengkap promo seperti caption, syarat, dan ketentuan
- Mendapatkan promo spesifik untuk varian tertentu jika kode varian disediakan

Input yang diperlukan:
- model_name: <PERSON>a model kendaraan (string, wajib diisi)
- variant_code: Ko<PERSON> varian kendaraan (string, opsional)
- config: Konfigurasi runnable langchain untuk mendapatkan organization

Output:
- Data promo dalam format JSON yang mudah dibaca
- Informasi detail promo termasuk caption, syarat, dan ketentuan
- Error message jika model tidak ditemukan atau tidak ada promo tersedia

CONTOH PERTANYAAN:
- "Promo motor BeAT apa saja yang tersedia?"
- "Ada promo untuk Honda Vario 125 varian tertentu?"
- "Promo terbaru untuk motor PCX 160"
- "Cek promo Honda Scoopy dengan kode varian F1C02N46L2A/T"
- "Promo apa saja untuk motor Honda di dealer?"
- "Informasi promo Honda CB150R"
- "Promo khusus untuk Honda ADV 150"
"""
)


class VehiclePromoSchema(BaseModel):
    """
    Schema untuk input parameter tool get_vehicle_promo.

    Attributes:
        model_name (str): Nama model kendaraan yang valid
        variant_code (Optional[str]): Kode varian kendaraan (opsional)
    """

    model_name: str = Field(
        ...,
        description="Nama model kendaraan yang ingin dicari promonya",
    )
    variant_code: Optional[str] = Field(
        default=None,
        description="Kode varian kendaraan (opsional). Jika tidak disediakan, akan menampilkan promo umum untuk model tersebut.",
    )


def _format_promo_output(
    promo_data: Dict[str, Any], model_name: str, variant_code: Optional[str] = None
) -> str:
    """
    Format output promo menjadi string yang mudah dibaca dengan informasi lengkap.

    Args:
        promo_data (Dict[str, Any]): Data promo dari API
        model_name (str): Nama model kendaraan
        variant_code (Optional[str]): Kode varian kendaraan

    Returns:
        str: Output yang terformat dengan AREA, kode promo, variants, dan simulasi kredit
    """
    if not promo_data.get("success", False):
        return f"Gagal mengambil data promo untuk model {model_name}"

    promo_list = promo_data.get("data", [])
    if not promo_list:
        return f"Tidak ada promo yang tersedia untuk model {model_name}"

    # Jika data adalah dict tunggal, ubah menjadi list
    if isinstance(promo_list, dict):
        promo_list = [promo_list]

    result = f"=== PROMO UNTUK {model_name.upper()} ==="
    if variant_code:
        result += f" (Varian: {variant_code})"
    result += "\n\n"

    for i, promo in enumerate(promo_list, 1):
        result += f"PROMO {i}:\n"

        # Caption/Judul Promo
        caption = promo.get("caption", "Tidak ada judul")
        result += f"Judul: {caption}\n"

        # AREA
        areas = promo.get("area", [])
        if areas:
            # Handle both string and dict formats for area
            area_names = []
            for area in areas:
                if isinstance(area, dict):
                    area_names.append(area.get("name", "").upper())
                else:
                    area_names.append(str(area).upper())
            area_str = ", ".join(filter(None, area_names))
            if area_str:
                result += f"Area: {area_str}\n"

        # KODE PROMO
        promo_codes = promo.get("promo_codes", [])
        if promo_codes:
            codes_str = ", ".join(promo_codes)
            result += f"Kode Promo: {codes_str}\n"

        # KENDARAAN (Daftar Variants)
        vehicle_info = promo.get("vehicle", {})
        if vehicle_info:
            result += "\nINFORMASI KENDARAAN:\n"

            # Model name
            model_name_api = vehicle_info.get("model_name")
            if model_name_api:
                result += f"  Model: {model_name_api}\n"

            # Variants
            variant_custom = vehicle_info.get("variant_custom", [])
            if variant_custom:
                result += "  Varian Tersedia:\n"
                for variant in variant_custom:
                    variant_name = variant.get("variant_name", "N/A")
                    variant_code_api = variant.get("variant_code", "N/A")
                    color_name = variant.get("variant_color_name", "N/A")
                    color_code = variant.get("variant_color_code", "N/A")
                    result += f"    - {variant_name} ({variant_code_api}) - {color_name} ({color_code})\n"

        # SIMULASI KREDIT DAN LEASING
        credit_info = promo.get("credit", [])
        if credit_info:
            result += "\nSIMULASI KREDIT & LEASING:\n"
            for j, credit in enumerate(credit_info, 1):
                finco_name = credit.get("finco_name", "N/A")
                finco_code = credit.get("finco_code", "N/A")
                dp_amount = credit.get("dp_amount", 0)
                original_dp = dp_amount
                discount = promo.get("total_promo_discount", 0)
                final_dp = dp_amount - discount
                installment = credit.get("installment_amount", 0)
                tenor = credit.get("tenor", [])

                result += f"  Opsi {j}: {finco_name} ({finco_code})\n"
                result += f"    DP Awal: Rp {original_dp}\n"
                result += f"    Potongan Promo: Rp {discount}\n"
                result += f"    DP Akhir: Rp {final_dp}\n"
                result += f"    Angsuran: Rp {installment}\n"
                if tenor:
                    tenor_str = ", ".join([f"{t} bulan" for t in tenor])
                    result += f"    Tenor: {tenor_str}\n"

        # Periode berlaku
        start_period = promo.get("start_period")
        end_period = promo.get("end_period")
        if start_period and end_period:
            result += f"\nPeriode: {start_period[:10]} s/d {end_period[:10]}\n"

        # Total diskon
        total_discount = promo.get("total_promo_discount", 0)
        if total_discount > 0:
            result += f"Total Diskon Promo: Rp {total_discount}\n"

        result += "\n---\n\n"

    return result.strip()


async def _aget_vehicle_promo_tool(
    model_name: str, variant_code: Optional[str] = None, config: RunnableConfig = None
) -> str:
    """
    Fungsi asinkron untuk mengambil data promo kendaraan.

    Args:
        model_name (str): Nama model kendaraan
        variant_code (Optional[str]): Kode varian kendaraan (opsional)
        config (RunnableConfig): Konfigurasi runnable untuk mendapatkan organization

    Returns:
        str: Data promo dalam format yang mudah dibaca

    Raises:
        ToolException: Jika terjadi error saat mengambil data
    """
    try:
        # Validasi input
        if not model_name or not model_name.strip():
            raise ToolException("model_name tidak boleh kosong")

        # Bersihkan input
        model_name = model_name.strip()
        if variant_code:
            variant_code = variant_code.strip()
            if not variant_code:
                variant_code = None

        # Dapatkan organization dari config
        organization = "amarta"  # default
        if config and config.get("configurable"):
            organization = config.get("configurable", {}).get("organization", "amarta")

        # Inisialisasi service
        service = GetPromoService()

        # Panggil API untuk mendapatkan promo
        promo_response = await service.aget_promo_by_model_and_variant(
            organization=organization,
            model_name=model_name,
            variant_code=variant_code
            or "",  # API mungkin memerlukan string kosong daripada None
            ai=True,
        )

        if not promo_response:
            raise ToolException("Response API kosong atau tidak valid")

        # Format output
        return _format_promo_output(promo_response, model_name, variant_code)

    except ToolException:
        raise
    except Exception as e:
        variant_info = f" dengan varian {variant_code}" if variant_code else ""
        error_msg = (
            f"Gagal mengambil data promo untuk model {model_name}{variant_info}: {str(e)}. "
            "Promo mungkin tidak tersedia untuk model atau varian yang diminta."
        )
        raise ToolException(error_msg)


get_vehicle_promo_tool = StructuredTool.from_function(
    name="get_vehicle_promo",
    coroutine=_aget_vehicle_promo_tool,
    args_schema=VehiclePromoSchema,
    description=_DESCRIPTION,
    return_direct=False,
)

if __name__ == "__main__":
    import asyncio

    async def main():
        result = await get_vehicle_promo_tool.ainvoke(
            {"model_name": "scoopy", "variant_code": "F1C02N46L2A/T"}
        )
        print(result)

    asyncio.run(main())
