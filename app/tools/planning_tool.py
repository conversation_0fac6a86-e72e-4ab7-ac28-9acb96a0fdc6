import textwrap
from typing import Optional, List

from langchain_core.messages import ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import StructuredTool, InjectedToolCallId
from langgraph.types import Command
from pydantic import BaseModel, Field
from typing_extensions import Annotated

from app.utils.plan_execution import (
    format_execution_status_message,
)

_DESCRIPTION = textwrap.dedent(
    """
Tool untuk merekam rencana jangka pendek (short-term plan) untuk menyelesaikan masalah user.
Rencana ini bersifat jangka pendek karena dapat berubah atau disesuaikan seiring dengan perkembangan interaksi.

Tool ini digunakan untuk:
- Merekam objektif dan keinginan user secara ringkas dan terstruktur.
- Menyusun langkah-langkah sistematis yang fleksibel untuk menyelesaikan masalah user.
- Melacak kemajuan (tracking progress) dan menjalankan eksekusi berkelanjutan (continuous execution) hingga semua langkah selesai.
- Mengkoordinasikan penggunaan tool lain sesuai dengan rencana yang dibuat.
- Memberikan pembaruan status dan langkah selanjutnya kepada user.

Input yang diperlukan:
- intent: Ringkasan keinginan/masalah yang ingin diselesaikan user secara detail dari awal percakapan (string, wajib diisi).
- plans: Daftar rencana detail untuk menyelesaikan masalah beserta tool yang relevan (array, wajib diisi).
- finished_plans: Daftar status langkah yang sudah selesai (array, opsional).
- tool_choice: Tool yang harus dipanggil sekarang (string, opsional).
- plan_now: Rencana yang sekarang dilakukan (string, opsional).

Output:
- Status eksekusi dan kemajuan penyelesaian masalah.
- Informasi langkah selanjutnya yang akan dijalankan.
- Pembaruan tentang tool yang sedang atau akan dipanggil.
- Konfirmasi penyelesaian jika semua rencana sudah selesai.

"""
)


class PlanningToolSchema(BaseModel):
    """Schema untuk tool perencanaan jangka pendek (short-term plan) dan pelacakan kemajuan dengan eksekusi berkelanjutan."""

    intent: str = Field(
        ...,
        description="Ringkasan keinginan user. Apa yang sebenarnya ditanyakan atau diminta oleh user.",
        examples=[
            "User ingin membuat simulasi kredit motor dengan DP 3 juta",
            "User membutuhkan informasi varian motor Honda di Jakarta",
            "User ingin menyimpan data domisili kota untuk personalisasi",
            "User meminta bantuan troubleshooting error pada aplikasi",
        ],
    )

    plans: List[str] = Field(
        ...,
        description="Daftar rencana LLM untuk memecahkan masalah. Setiap item adalah langkah detail yang harus dieksekusi beserta tool relevan yang harus digunakan.",
        examples=[
            [
                "Panggil get_variants_tool dengan parameter city",
                "Tampilkan hasil varian motor",
            ],
            [
                "Validasi input user",
                "Panggil API simulasi kredit",
                "Format dan tampilkan hasil",
            ],
        ],
    )

    plan_now: Optional[str] = Field(
        None,
        description="Rencana yang sekarang dilakukan.",
        examples=[
            "Panggil get_variants_tool dengan parameter city",
            "Tampilkan hasil varian motor",
            "Validasi input user",
        ],
    )

    finished_plans: Optional[List[str]] = Field(
        None,
        description="Daftar status langkah yang sudah diselesaikan. Setiap item menunjukkan langkah yang telah selesai.",
        examples=[
            ["Step 1: completed", "Step 2: completed"],
            ["Step 1: completed", "Step 2: failed - API error"],
        ],
    )

    tool_choice: Optional[str] = Field(
        None,
        description="Tool yang harus dipanggil sekarang. Berikan nama tool yang akan dieksekusi pada langkah ini.",
        examples=[
            "get_variants_tool",
            "get_credit_simulation_tool",
            "save_user_city_domicile_tool",
        ],
    )

    tool_call_id: Annotated[str, InjectedToolCallId]


async def _aplanning_tool(
    intent: str,
    plans: List[str],
    tool_call_id: str,
    config: RunnableConfig,
    plan_now: Optional[str] = None,
    finished_plans: Optional[List[str]] = None,
    tool_choice: Optional[str] = None,
):
    """
    Tool perencanaan secara asinkron dengan continuous execution.

    Args:
        intent: Ringkasan keinginan/masalah user
        plans: Daftar rencana detail untuk menyelesaikan masalah
        tool_call_id: ID unik untuk tool call ini
        config: Konfigurasi runtime
        plan_now: Rencana yang sekarang dilakukan (optional)
        finished_plans: Daftar status langkah yang sudah diselesaikan (optional)
        tool_choice: Tool yang harus dipanggil sekarang (optional)

    Returns:
        str: Status eksekusi dan langkah selanjutnya

    Raises:
        Exception: Jika terjadi error dalam proses perencanaan
    """
    try:
        # Validasi input
        if not intent or not intent.strip():
            raise ValueError("Intent tidak boleh kosong")

        if not plans or len(plans) == 0:
            raise ValueError("Plans tidak boleh kosong")

        # Bersihkan input dari whitespace berlebih
        clean_intent = intent.strip()
        clean_plans = [plan.strip() for plan in plans if plan.strip()]
        clean_finished_plans = (
            [fp.strip() for fp in finished_plans if fp.strip()]
            if finished_plans
            else []
        )

        # Format pesan status
        status_message = format_execution_status_message(
            intent=clean_intent,
            plans=clean_plans,
            plan_now=plan_now,
            finished_plans=clean_finished_plans,
            tool_calling_now=tool_choice,
        )

        tool_message = ToolMessage(
            content=status_message,
            tool_call_id=tool_call_id,
        )

        # Return status message dengan informasi continuous execution
        return Command(
            update={
                "messages": [tool_message],
                "intent": clean_intent,
                "plans": clean_plans,
                "plan_now": plan_now,
                "finished_plans": clean_finished_plans,
                "tool_choice": tool_choice,
            },
        )

    except ValueError as ve:
        # Error validasi input
        return f"❌ Maaf, ada masalah dengan input perencanaan: {str(ve)}"

    except Exception as e:
        # Error umum lainnya
        return f"❌ Maaf, terjadi kesalahan saat memproses perencanaan: {str(e)}"


planning_tool = StructuredTool.from_function(
    name="planning_tool",
    coroutine=_aplanning_tool,
    args_schema=PlanningToolSchema,
    description=_DESCRIPTION,
    return_direct=False,
)
