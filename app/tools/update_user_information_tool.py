import asyncio
import logging
import textwrap
from textwrap import dedent
from typing import Optional

from langchain_core.messages import ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import StructuredTool, InjectedToolCallId, ToolException
from langgraph.types import Command
from pydantic import BaseModel, Field
from typing_extensions import Annotated

from app.helpers.determine_city_group import adetermine_city_group
from app.services.update_user_information_service import UpdateUserInformationService

_DESCRIPTION = textwrap.dedent(
    """
Tool ini digunakan untuk menyimpan/memperbarui nama dan kota domisili. Dapat digunakan bersamaan atau terpisah.

Tool ini berfungsi untuk:
- Menyimpan nama pengguna untuk komunikasi dan layanan yang dipersonalisasi.
- Menyimpan lokasi pengguna untuk layanan yang dipersonalisasi.
- Mengidentifikasi `city_group` berdasarkan lokasi pengguna.
- Menentukan area layanan yang sesuai berdasarkan lokasi pengguna.

Input yang diperlukan:
- real_name: <PERSON><PERSON> leng<PERSON> atau panggilan pengguna (string, opsional).
- city: Kota, alamat, atau deskripsi lokasi pengguna (string, opsional).

Output:
- Konfirmasi bahwa nama dan/atau lokasi telah berhasil disimpan.
- Pesan sapaan yang dipersonalisasi jika nama disimpan.
- Informasi tentang `city_group` yang ditentukan jika lokasi diberikan.
- Memperbarui state dengan nama dan/atau `city_group` pengguna untuk personalisasi lebih lanjut.
- Pesan kesalahan jika terjadi masalah saat menyimpan informasi.

CONTOH PERTANYAAN:
- \"Nama saya Budi Santoso dan saya tinggal di Jakarta\"
- \"Saya Ahmad Rahman dari Bandung\"
- \"Panggil saya Andi\"
- \"Saya dari Jakarta Selatan\"
- \"Domisili saya di kota kembang\"
"""
)


class UpdateUserInformationSchema(BaseModel):
    """
    Skema untuk parameter input dari tool update_user_information.

    Atribut:
        real_name (Optional[str]): Nama asli/lengkap atau nama panggilan pengguna.
        city (Optional[str]): Informasi tentang lokasi pengguna.
        tool_call_id (str): ID unik untuk pemanggilan tool (disuntikkan secara otomatis).
    """

    real_name: Optional[str] = Field(
        default=None,
        description="Nama asli/lengkap atau nama panggilan pengguna, yang disebutkan secara eksplisit.",
        min_length=1,
        max_length=100,
        examples=["Taufan Budi", "Andi"],
    )
    city: Optional[str] = Field(
        default=None,
        description="Kota, alamat, atau lokasi pengguna, yang disebutkan secara eksplisit.",
        examples=["Bandung", "Jakarta", "kota kembang"],
    )
    tool_call_id: Annotated[str, InjectedToolCallId]


async def _aupdate_user_information(
    config: RunnableConfig,
    tool_call_id: str,
    real_name: Optional[str] = None,
    city: Optional[str] = None,
) -> Command:
    """
    Secara asinkron menyimpan nama asli dan/atau informasi kota/domisili pengguna.
    """
    try:
        if not real_name and not city:
            raise ValueError("Harap berikan nama atau kota untuk disimpan.")

        chat_room_path = config.get("configurable", {}).get("chatroom_path")
        updates = {}
        confirmation_parts = []
        current_real_name = config.get("configurable", {}).get("real_name")

        organization_code = config.get("configurable", {}).get("organization", "amarta")

        organization = "amartahonda"
        organization_group = "amartamotor"

        if organization_code == "vinfast":
            organization = "amartavinfast"
            organization_group = "amartamobil"

        # Inisialisasi layanan
        user_service = UpdateUserInformationService()

        # 1. Proses dan simpan nama jika diberikan
        if real_name and real_name.strip():
            clean_name = real_name.strip()
            updates["real_name"] = clean_name
            current_real_name = clean_name  # Perbarui nama untuk pesan sapaan
            if chat_room_path:
                try:
                    await user_service.update_name(
                        chat_room_path=chat_room_path, name=clean_name
                    )
                    confirmation_parts.append(f"Nama '{clean_name}' berhasil disimpan.")
                except Exception as e:
                    logging.warning(f"Gagal menyimpan nama ke database: {e}")
                    confirmation_parts.append(f"Gagal menyimpan nama '{clean_name}'.")
            else:
                confirmation_parts.append(f"Nama '{clean_name}' telah dicatat.")

        # 2. Proses dan simpan kota jika diberikan
        if city and city.strip():
            clean_city = city.strip()
            get_city_group = await adetermine_city_group(clean_city, config)

            if get_city_group and get_city_group.area:
                updates["city_group"] = get_city_group.area
                if chat_room_path:
                    try:
                        await user_service.update_city_group(
                            chat_room_path=chat_room_path,
                            city_group=get_city_group.area,
                            organization=organization,
                            organization_group=organization_group,
                        )
                        msg = f"Lokasi '{clean_city}' berhasil diidentifikasi di area layanan: {get_city_group.area.title()}."
                        if get_city_group.reason:
                            msg += f" ({get_city_group.reason})"
                        confirmation_parts.append(msg)
                    except Exception as e:
                        logging.warning(f"Gagal menyimpan grup kota ke database: {e}")
                        confirmation_parts.append(
                            f"Gagal menyimpan lokasi '{clean_city}'."
                        )
                else:
                    confirmation_parts.append(
                        f"Lokasi '{clean_city}' diidentifikasi di area: {get_city_group.area.title()}."
                    )
            else:
                raise ValueError(
                    f"Tidak dapat menentukan area dari lokasi '{clean_city}'. "
                    f"Sepertinya kami belum tersedia di kota/alamat/area tersebut."
                )

        # 3. Buat pesan konfirmasi akhir
        content = "\n".join(confirmation_parts)
        if current_real_name:
            content += f"\nSenang berinteraksi dengan Anda, {current_real_name}!"

        tool_message = ToolMessage(content=dedent(content), tool_call_id=tool_call_id)
        updates["messages"] = [tool_message]

        return Command(update=updates)

    except ValueError as ve:
        raise ToolException(f"Input tidak valid: {str(ve)}")
    except Exception as e:
        raise ToolException(
            f"Terjadi kesalahan internal saat memproses permintaan Anda: {str(e)}"
        )


update_user_information_tool = StructuredTool.from_function(
    name="update_user_information",
    coroutine=_aupdate_user_information,
    args_schema=UpdateUserInformationSchema,
    description=_DESCRIPTION,
    return_direct=False,
)

if __name__ == "__main__":

    async def main():
        """
        Contoh penggunaan tool update_user_information dengan berbagai skenario.
        """
        print("=== CONTOH PENGGUNAAN TOOL ===")
        print("---" * 10)

        sample_config: RunnableConfig = {
            "configurable": {
                "chatroom_path": "/projects/WLdKug7hau0MbRzKcnqg/chat_rooms/6285763705624",
            }
        }

        # --- Skenario 1: Simpan Nama dan Kota ---
        print("\n1. SKENARIO: Nama dan Kota")
        print("   Pengguna: 'Nama saya Taufan Budi, saya dari Jakarta'")
        try:
            result1 = await update_user_information_tool.ainvoke(
                {"real_name": "Taufan Budi", "city": "Jakarta"},
                config=sample_config,
            )
            print(f"   Hasil: {result1}")
            # Perbarui config untuk mensimulasikan state
            if "real_name" in result1.update:
                sample_config["configurable"]["real_name"] = result1.update.get(
                    "real_name"
                )
        except Exception as e:
            print(f"   Error: {e}")

        # --- Skenario 2: Simpan Hanya Nama ---
        print("\n2. SKENARIO: Hanya Nama")
        print("   Pengguna: 'Panggil saya Andi'")
        try:
            result2 = await update_user_information_tool.ainvoke(
                {"real_name": "Andi"},
                config=sample_config,
            )
            print(f"   Hasil: {result2}")
            if "real_name" in result2.update:
                sample_config["configurable"]["real_name"] = result2.update.get(
                    "real_name"
                )
        except Exception as e:
            print(f"   Error: {e}")

        # --- Skenario 3: Simpan Hanya Kota ---
        print("\n3. SKENARIO: Hanya Kota")
        print("   Pengguna: 'Saya tinggal di kota kembang'")
        try:
            result3 = await update_user_information_tool.ainvoke(
                {"city": "kota kembang"},
                config=sample_config,
            )
            print(f"   Hasil: {result3}")
        except Exception as e:
            print(f"   Error: {e}")

        # --- Skenario 4: Perbarui Nama yang Ada ---
        print("\n4. SKENARIO: Perbarui Nama")
        print("   Pengguna: 'Nama lengkap saya Andi Wijaya'")
        try:
            result4 = await update_user_information_tool.ainvoke(
                {"real_name": "Andi Wijaya"},
                config=sample_config,
            )
            print(f"   Hasil: {result4}")
            if "real_name" in result4.update:
                sample_config["configurable"]["real_name"] = result4.update.get(
                    "real_name"
                )
        except Exception as e:
            print(f"   Error: {e}")

        # --- Skenario 5: Input Kosong (Diharapkan Error) ---
        print("\n5. SKENARIO: Input Kosong (Diharapkan Error)")
        print("   Pengguna: '...' (input kosong)")
        try:
            result5 = await update_user_information_tool.ainvoke(
                {},
                config=sample_config,
            )
            print(f"   Hasil: {result5}")
        except Exception as e:
            print(f"   Error: {e}")

    asyncio.run(main())
