from fastapi import APIRouter

from app.handlers.summary_handler import summary_handler, SummaryReqBody
from app.schemas.base_response import SuccessResponse

router = APIRouter()


@router.post(
    "/summary/ideal", tags=["summary", "ideal"], response_model=SuccessResponse
)
async def summary_ideal(body: SummaryReqBody):
    """
    Endpoint untuk meringkas percakapan dari collection ai_agent_chats.
    Semua dokumen akan dihapus dan diganti dengan satu dokumen ringkasan.

    Args:
        body: Request body yang berisi chatroom_path

    Returns:
        SuccessResponse dengan ringkasan percakapan
    """
    return await summary_handler(body)
