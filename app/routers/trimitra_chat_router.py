from fastapi import APIRouter

from app.handlers.trimitra_chat_handler import (
    trimitra_chat_handler,
    TrimitraChatReqBody,
)
from app.schemas.base_response import SuccessResponse

router = APIRouter()


@router.post(
    "/chat/trimitra", tags=["chat", "trimitra"], response_model=SuccessResponse
)
async def chat_trimitra(body: TrimitraChatReqBody):
    """Endpoint for chatting with Agent Trimitra.

    Agent <PERSON><PERSON><PERSON> ad<PERSON> asisten AI general purpose yang dapat membantu
    dengan berbagai topik percakapan umum.

    Args:
        body: Request body containing message and conversation details

    Returns:
        SuccessResponse: Response containing agent's reply and metadata
    """
    return await trimitra_chat_handler(body)
