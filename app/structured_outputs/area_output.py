from typing import Optional, List

from pydantic import BaseModel, Field


class AreaOutput(BaseModel):
    """Gunakan untuk menentukan area berdasarkan alamat yang diberikan."""

    area: Optional[str] = Field(
        default=None,
        description="Area yang cocok untuk user",
    )
    reason: str = Field(
        ...,
        description="Alasan singkat mengapa area tersebut dipilih.",
    )
