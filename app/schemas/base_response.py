from typing import Any, Optional

from pydantic import BaseModel, Field


class SuccessResponse(BaseModel):
    status_code: int = 200
    success: bool = True
    message: str = "Request successful"
    data: Optional[Any]
    metadata: Optional[Any]

    def __init__(
        self,
        status_code: int = 200,
        success: bool = True,
        message: str = "Request successful",
        data: Optional[Any] = None,
        metadata: Optional[Any] = None,
    ):
        super().__init__(
            status_code=status_code,
            success=success,
            message=message,
            data=data,
            metadata=metadata,
        )


class ErrorResponse(Exception):
    success: bool
    message: str
    status_code: int
    errors: Optional[Any] = Field(
        None, description="Any error object related to the request"
    )

    def __init__(
        self,
        success: bool = False,
        message: str = "Request failed",
        status_code: int = 500,
        errors: Optional[Any] = None,
    ):
        self.success = success
        self.message = message
        self.status_code = status_code
        self.errors = errors
