from typing import List

from langchain_core.callbacks import UsageMetadataCallbackHandler
from langchain_core.messages import HumanMessage, BaseMessage
from pydantic import BaseModel

from app.agents.whatsapp_basic_agent_chat import WhatsappBasicAgentChat
from app.helpers.firestore_ai_agent_messages import (
    load_ai_agent_chat_room,
    replace_ai_agent_chat_room,
)
from app.llm.gemini import gemini
from app.schemas.base_response import SuccessResponse


class _Messages(BaseModel):
    text: str
    name: str | None = None


class BasicChatReqBody(BaseModel):
    message: _Messages
    chatroom_path: str
    real_name: str | None = None
    city_group: str | None = None
    connected_to_admin: bool | None = None

    organization: str | None = "amarta"


async def ideal_chat_handler(body: BasicChatReqBody) -> SuccessResponse:
    messages: List[BaseMessage] = []

    # load message
    if body.chatroom_path is not None:
        messages = await load_ai_agent_chat_room(body.chatroom_path)

    # Process message (role removed, always treated as user message)
    message = body.message
    messages.append(HumanMessage(content=message.text, name=message.name))

    callbacks = UsageMetadataCallbackHandler()

    agent = WhatsappBasicAgentChat()

    chatroom_path = body.chatroom_path

    config = {
        "callbacks": [callbacks],
        "configurable": {
            "model": gemini,
            "chatroom_path": body.chatroom_path,
            "organization": body.organization,
        },
    }

    response = await agent.runnable.ainvoke(
        {
            "messages": messages,
            "real_name": body.real_name,
            "city_group": body.city_group,
            "connected_to_admin": body.connected_to_admin,
        },
        config,
    )

    await replace_ai_agent_chat_room(chatroom_path, response["messages"])

    return SuccessResponse(
        data={"response": response["final_answer"]}, metadata=callbacks.usage_metadata
    )
