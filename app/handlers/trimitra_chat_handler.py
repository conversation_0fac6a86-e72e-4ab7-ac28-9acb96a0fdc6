from typing import List

from langchain_core.callbacks import UsageMetadataCallbackHandler
from langchain_core.messages import HumanMessage, BaseMessage
from pydantic import BaseModel

from app.agents.agent_trimitra import AgentTrimitra
from app.helpers.firestore_ai_agent_messages import (
    load_ai_agent_chat_room,
    replace_ai_agent_chat_room,
)
from app.schemas.base_response import SuccessResponse


class _Messages(BaseModel):
    text: str
    name: str | None = None


class TrimitraChatReqBody(BaseModel):
    message: _Messages
    user_name: str | None = None
    thread_id: str | None = None


async def trimitra_chat_handler(body: TrimitraChatReqBody) -> SuccessResponse:
    """Handler for Agent Trimitra chat requests."""

    messages: List[BaseMessage] = []

    # Process new message from user
    message = body.message
    messages.append(HumanMessage(content=message.text, name=message.name))

    # Setup callbacks for usage tracking
    callbacks = UsageMetadataCallbackHandler()

    # Initialize Agent Trimitra
    agent = AgentTrimitra()

    # Configuration for the agent
    config = {
        "callbacks": [callbacks],
        "configurable": {
            "user_name": body.user_name or "User",
            "thread_id": body.thread_id or "",
        },
    }

    # Invoke the agent with current state
    result = await agent.runnable.ainvoke(
        {
            "messages": messages,
            "user_name": body.user_name or "User",
            "thread_id": body.thread_id or "",
            "is_active": True,
            "last_response": "",
        },
        config,
    )

    # Get the final messages including the agent's response
    final_messages = result["messages"]

    # Get the last message (agent's response)
    last_message = final_messages[-1] if final_messages else None
    response_text = ""

    if last_message:
        if hasattr(last_message, "content"):
            response_text = last_message.content
        else:
            response_text = str(last_message)

    # Prepare response data
    response_data = {
        "agent_response": response_text,
        "thread_id": body.thread_id or "",
        "user_name": body.user_name or "User",
        "message_count": len(final_messages),
        "usage_metadata": {
            "input_tokens": getattr(callbacks, "input_tokens", 0),
            "output_tokens": getattr(callbacks, "output_tokens", 0),
            "total_tokens": getattr(callbacks, "total_tokens", 0),
        },
    }

    return SuccessResponse(
        message="Agent Trimitra response generated successfully", data=response_data
    )
