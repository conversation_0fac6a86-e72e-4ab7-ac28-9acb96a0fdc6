import json
import logging
from json import dumps
from typing import List, Any, Coroutine

import devtools
from langchain_core.callbacks import UsageMetadataCallbackHandler
from langchain_core.messages import BaseMessage, AIMessage, ToolMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel

from app.helpers.firestore_ai_agent_messages import (
    load_chat_room_messages,
    replace_ai_agent_chat_room,
)
from app.llm.gemini import gemini
from app.schemas.base_response import SuccessResponse, ErrorResponse


class SummaryReqBody(BaseModel):
    chatroom_path: str
    replace: bool = False


async def summary_handler(body: SummaryReqBody) -> SuccessResponse:
    """
    Handler untuk meringkas percakapan dari collection ai_agent_chats.
    Semua dokumen akan dihapus dan diganti dengan satu dokumen ringkasan.
    """

    # Load semua pesan dari ai_agent_chats collection
    messages: List = await load_chat_room_messages(body.chatroom_path)

    if not messages:
        raise ErrorResponse(message="Tidak ada pesan ditemukan")

    # Buat prompt untuk meringkas percakapan
    conversation_text = ""
    for msg in messages:
        content = msg.get("content")
        del content["timestamp"]
        conversation_text += f"{msg.get('role')}: {json.dumps(content)}\n"

    summary_prompt = f"""
Tugas kamu adalah meringkas percakapan berikut yang melibatkan Konsumen(user), Asisten AI, dan Admin, dalam bahasa Indonesia.
Tempatkan kamu sebagai sudut pandang `Asisten AI`.

Responmu harus diformat seperti ini:
"Ringkasan Percakapan Sebelumnya:
<isi ringkasan>"

Jangan meringkas tentang:
- Meringkas hal yang lampau.
- Kamu menawarkan hubungkan ke admin, dan.
- Keputusan konsumen untuk menghubungi ke admin

Pastikan untuk:
- Hanya meringkas hal yang baru.
- Menggunakan paragraf yang jelas.
- Meringkas poin penting saja.

Point penting:
- Pertanyaan/Intent user
- Jawaban dari pertanyaan user

Percakapan yang akan diringkas:
{conversation_text}"""

    callback = UsageMetadataCallbackHandler()
    config: RunnableConfig = {
        "callbacks": [callback],
    }

    # Generate ringkasan menggunakan Gemini
    try:
        model = gemini
        summary_response = await model.ainvoke(summary_prompt, config=config)
        summary_content = summary_response.text()
    except Exception as e:
        raise ErrorResponse(
            status_code=500,
            message=f"Gagal membuat ringkasan: {str(e)}",
        )

    # Buat AIMessage dengan ringkasan
    summary_message = AIMessage(content=summary_content)

    # Replace semua pesan dengan satu pesan ringkasan
    if body.replace:
        try:
            await replace_ai_agent_chat_room(body.chatroom_path, [summary_message])
        except Exception as e:
            logging.error(f"Error replacing chat room: {e}")
            raise ErrorResponse(message=f"Gagal menyimpan ringkasan: {str(e)}")

    # devtools.debug(summary_message.text())

    return SuccessResponse(
        data={
            "summary": summary_content,
            "original_message_count": len(messages),
            "chatroom_path": body.chatroom_path,
        },
        metadata=callback.usage_metadata,
    )
