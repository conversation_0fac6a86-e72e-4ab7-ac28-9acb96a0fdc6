### Test Agent Tri<PERSON>ra Chat Endpoint

# Basic chat test
POST http://localhost:8000/chat/trimitra
Content-Type: application/json

{
  "message": {
    "text": "Halo Agent Trimitra, apa kabar?",
    "name": "User"
  },
  "user_name": "Test User",
  "thread_id": "conv_001"
}

###

# Follow-up conversation test
POST http://localhost:8000/chat/trimitra
Content-Type: application/json

{
  "message": {
    "text": "Bisakah kamu membantu saya memahami tentang artificial intelligence?",
    "name": "User"
  },
  "user_name": "Test User",
  "thread_id": "conv_001"
}

###

# General knowledge test
POST http://localhost:8000/chat/trimitra
Content-Type: application/json

{
  "message": {
    "text": "Apa itu machine learning dan bagaimana cara kerjanya?",
    "name": "User"
  },
  "user_name": "Test User 2",
  "thread_id": "conv_002"
}

###

# Minimal request test
POST http://localhost:8000/chat/trimitra
Content-Type: application/json

{
  "message": {
    "text": "Terima kasih!"
  }
}

###

# Indonesian language test
POST http://127.0.0.1:8000/chat/trimitra
Content-Type: application/json

{
  "message": {
    "text": "Hello, AI!",
    "name": "Test User"
  },
  "user_name": "tesstuser",
  "thread_id": "thread123"
}
