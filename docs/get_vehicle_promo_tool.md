# Get Vehicle Promo Tool

Tool untuk mendapatkan informasi promo kendaraan berdasarkan model dan varian.

## Deskripsi

`get_vehicle_promo_tool` adalah tool yang memungkinkan AI agent untuk mengambil data promo kendaraan dari API berdasarkan nama model dan kode varian (opsional). Tool ini menggunakan service `GetPromoService` dengan fungsi `aget_promo_by_model_and_variant`.

## Parameter

### Input Parameters

1. **`model_name`** (required)
   - **Type**: `str`
   - **Description**: Nama model kendaraan yang ingin dicari promonya
   - **Example**: `"scoopy"`, `"beat"`, `"vario 125"`, `"pcx 160"`

2. **`variant_code`** (optional)
   - **Type**: `Optional[str]`
   - **Description**: Kode varian kendaraan (opsional). Jika tidak disediakan, akan menampilkan promo umum untuk model tersebut
   - **Example**: `"F1C02N46L2A/T"`, `"ABC123DEF"`
   - **Default**: `None`

3. **`config`** (required by LangChain)
   - **Type**: `RunnableConfig`
   - **Description**: Konfigurasi runnable langchain untuk mendapatkan organization
   - **Usage**: Digunakan untuk mendapatkan `organization` dari `config.configurable.organization`

## Output

Tool ini mengembalikan string yang berisi informasi promo lengkap dalam format yang mudah dibaca:

```
=== PROMO UNTUK SCOOPY === (Varian: F1C02N46L2A/T)

PROMO 1:
Judul: SCOOPY DP 800 Khusus KTP BEKASI
Area: BEKASI
Kode Promo: JABARSCOOPY25L

INFORMASI KENDARAAN:
  Model: Scoopy
  Varian Tersedia:
    - SCOOPY ENERGETIC (F1C02N46L2A/T) - merah hitam (MH)
    - SCOOPY ENERGETIC (F1C02N46L2A/T) - silver hitam (SH)

SIMULASI KREDIT & LEASING:
  Opsi 1: PT FEDERAL INTERNATIONAL FINANCE (FIFG)
    DP Awal: Rp 3000000
    Potongan Promo: Rp 2200000
    DP Akhir: Rp 800000
    Angsuran: Rp 1039000
    Tenor: 35 bulan

Periode: 2025-07-21 s/d 2025-08-31
Total Diskon Promo: Rp 2200000

---

```

## Penggunaan

### Import Tool

```python
from app.tools.get_vehicle_promo_tool import get_vehicle_promo_tool
```

### Contoh Penggunaan Langsung

```python
import asyncio
from langchain_core.runnables import RunnableConfig
from app.tools.get_vehicle_promo_tool import _aget_vehicle_promo_tool

async def example_usage():
    config = RunnableConfig(configurable={"organization": "amarta"})
    
    # Dengan model saja
    result1 = await _aget_vehicle_promo_tool(
        model_name="scoopy",
        config=config
    )
    print(result1)
    
    # Dengan model dan variant code
    result2 = await _aget_vehicle_promo_tool(
        model_name="scoopy",
        variant_code="F1C02N46L2A/T",
        config=config
    )
    print(result2)

asyncio.run(example_usage())
```

### Contoh Penggunaan dengan StructuredTool

```python
import asyncio
from langchain_core.runnables import RunnableConfig
from app.tools.get_vehicle_promo_tool import get_vehicle_promo_tool

async def example_structured_tool():
    config = RunnableConfig(configurable={"organization": "amarta"})
    
    result = await get_vehicle_promo_tool.ainvoke(
        {
            "model_name": "beat",
            "variant_code": None
        },
        config=config
    )
    print(result)

asyncio.run(example_structured_tool())
```

## Contoh Pertanyaan yang Dapat Dijawab

- "Promo motor BeAT apa saja yang tersedia?"
- "Ada promo untuk Honda Vario 125 varian tertentu?"
- "Promo terbaru untuk motor PCX 160"
- "Cek promo Honda Scoopy dengan kode varian F1C02N46L2A/T"
- "Promo apa saja untuk motor Honda di dealer?"
- "Informasi promo Honda CB150R"
- "Promo khusus untuk Honda ADV 150"

## Error Handling

Tool ini menangani berbagai jenis error:

1. **Input Validation Error**:
   ```
   ToolException: model_name tidak boleh kosong
   ```

2. **API Error**:
   ```
   ToolException: Gagal mengambil data promo untuk model beat: Client error '422'
   ```

3. **Network Error**:
   ```
   ToolException: Gagal mengambil data promo untuk model scoopy: Connection timeout
   ```

## Dependencies

- `langchain_core.runnables.RunnableConfig`
- `langchain_core.tools.StructuredTool`
- `langchain_core.tools.ToolException`
- `pydantic.BaseModel`
- `pydantic.Field`
- `app.services.get_promo_service.GetPromoService`

## Testing

Untuk menjalankan test:

```bash
# Dari root directory project
PYTHONPATH=/path/to/project python test/test_get_vehicle_promo_tool.py
```

Test mencakup:
- Test dengan model saja
- Test dengan model dan variant code
- Test menggunakan StructuredTool interface
- Test dengan input tidak valid

## File Structure

```
app/tools/
├── get_vehicle_promo_tool.py    # Tool implementation
test/
├── test_get_vehicle_promo_tool.py    # Test file
docs/
├── get_vehicle_promo_tool.md    # Documentation (this file)
```

## Notes

- Tool ini menggunakan `GetPromoService.aget_promo_by_model_and_variant()` untuk mendapatkan data promo
- Parameter `ai=True` selalu digunakan saat memanggil API
- Organization default adalah "amarta" jika tidak disediakan dalam config
- Output diformat agar mudah dibaca oleh user
- Tool mendukung async operation untuk performa yang lebih baik