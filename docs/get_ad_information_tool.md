# Get Ad Information Tool

## Deskripsi

Tool `get_ad_information` digunakan untuk mengambil informasi iklan berdasarkan ID iklan (`source_id`) atau kode promo (`promo_code`). Tool ini memberikan informasi lengkap tentang promo kendaraan, termasuk detail kendaraan, opsi kredit, area berlaku, dan periode promo.

## Strategi Pencarian

Tool ini menerapkan strategi prioritas dalam pencarian data:
1. **Prioritas Pertama**: Jika `source_id` disediakan, tool akan mencari berdasarkan `source_id` terlebih dahulu
2. **Fallback**: Jika `source_id` tidak ditemukan atau tidak valid, tool akan mencari berdasarkan `promo_code` (jika disediakan)

## Parameter Input

Tool ini menerima dua parameter opsional, namun setidaknya satu harus diisi:

| Parameter | Tipe | Deskripsi | Format | Wajib |
|-----------|------|-----------|--------|-------|
| `source_id` | string (opsional) | ID iklan yang ingin dicari informasinya | Hanya angka | Tidak* |
| `promo_code` | string (opsional) | Kode promo yang ingin dicari informasinya | Kombinasi angka dan huruf | Tidak* |

*Keterangan: Setidaknya salah satu dari `source_id` atau `promo_code` harus diisi.

## Validasi Input

- `source_id`: Harus berupa string yang hanya mengandung angka (regex: `^\d+$`)
- `promo_code`: Harus berupa string dengan kombinasi angka dan huruf
- Kedua parameter memiliki panjang minimum 1 karakter dan maksimum 50 karakter

## Output

Tool ini mengembalikan informasi iklan dalam format yang mudah dibaca, mencakup:

1. **Informasi Umum Iklan**:
   - Judul promo
   - Deskripsi promo
   - Metode pembelian

2. **Informasi Area**:
   - Daftar area berlakunya promo
   - Alamat default (kota dan provinsi)

3. **Informasi Kode Promo**:
   - Daftar kode promo yang berlaku

4. **Informasi Kendaraan**:
   - Nama model kendaraan
   - Varian yang tersedia:
     - Nama varian
     - Kode varian
     - Warna varian
     - Link produk

5. **Informasi Kredit**:
   - Opsi kredit yang tersedia:
     - Jumlah DP
     - Tenor (dalam bulan)
     - Jumlah cicilan
     - Nama perusahaan pembiayaan

## Contoh Penggunaan

### Menggunakan Source ID
```json
{
  "source_id": "6730637160071"
}
```

### Menggunakan Promo Code
```json
{
  "promo_code": "SCOOPYDP800CRB"
}
```

### Menggunakan Kombinasi (Source ID sebagai prioritas)
```json
{
  "source_id": "6730637160071",
  "promo_code": "SCOOPYDP800CRB"
}
```

## Error Handling

Tool ini akan memberikan pesan error yang informatif dalam kondisi berikut:
- Jika kedua parameter (`source_id` dan `promo_code`) tidak diisi
- Jika `source_id` tidak ditemukan dan `promo_code` tidak ditemukan
- Jika terjadi kesalahan koneksi ke API
- Jika format input tidak sesuai dengan ketentuan validasi

## Service yang Digunakan

Tool ini menggunakan service `GetPromoService` dengan method:
- `aget_promo_from_ad_source_id(source_id)` untuk pencarian berdasarkan `source_id`
- `aget_promo_by_code(code)` untuk pencarian berdasarkan `promo_code`

## Contoh Output

```
Judul: SCOOPY DP 800 Khusus KTP BEKASI

Deskripsi: *SCOOPY ENERGETIC*
Harga: Rp23.250.000 
Area: BEKASI 

promo scoopy kunci dp 800rb 35x angs 1.039.000

Metode Pembelian: credit
Area: Bekasi
Alamat Default: Kota Bekasi, Jawa Barat
Kode Promo: JABARSCOOPY25L

Model: Scoopy
Varian Tersedia:
  - Variant: SCOOPY ENERGETIC
    Kode: F1C02N46L2A/T
    Warna: merah hitam
    Product link: https://amartahonda.com/baru/bekasi/F1C02N46L2AT

Opsi Kredit:
Opsi 1:
  DP: Rp800,000
  Tenor: 35 bulan
  Cicilan: Rp1,039,000
  Finco: PT FEDERAL INTERNATIONAL FINANCE
Opsi 2:
  DP: Rp800,000
  Tenor: 35 bulan
  Cicilan: Rp1,039,000
  Finco: PT OTO FINANCE
```