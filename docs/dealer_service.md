# Dealer Service

## Deskripsi

Service `DealerService` bertanggung jawab untuk mengambil informasi mengenai data dealer dari <PERSON>. Service ini menyediakan metode untuk mendapatkan data dealer berdasarkan organisasi tertentu, seperti "vinfast" atau "amarta".

## Komponen

Service ini didefinisikan dalam `app/services/dealer_service.py` dan memiliki metode utama:

1.  **`aget_dealer_data(organization)`**
    *   **Tujuan**: Mengambil data dealer berdasarkan organisasi yang ditentukan.
    *   **Argumen**: `organization` (string) - Nama organisasi yang dapat berupa "vinfast" atau "amarta".
    *   **Endpoint**: `/project/{organization}`
    *   **Validasi**: Input organization harus berupa "vinfast" atau "amarta", jika tidak akan menimbulkan ValueError.

## Alur Kerja

1.  Komponen lain yang membutuhkan informasi dealer akan memanggil metode `aget_dealer_data()` dari `DealerService`.
2.  Service akan memvalidasi parameter `organization` untuk memastikan nilainya adalah "vinfast" atau "amarta".
3.  <PERSON><PERSON> validasi ber<PERSON>, metode akan membuat permintaan GET ke endpoint `/project/{organization}` menggunakan `amarta_api_client`.
4.  Service akan menerima respons dalam format JSON dari API.
5.  Jika permintaan tidak berhasil, sebuah `HTTPStatusError` akan dimunculkan.
6.  Jika berhasil, data JSON yang berisi informasi dealer akan dikembalikan ke pemanggil.

## Contoh Penggunaan

```python
import asyncio
from app.services.dealer_service import DealerService


async def main():
    service = DealerService()

    try:
        # 1. Mengambil data dealer untuk organisasi amarta
        print("--- Mengambil data dealer untuk amarta ---")
        amarta_data = await service.aget_dealer_data("amarta")
        print(f"Success: {amarta_data.get('success')}")
        print(f"Total dealers: {len(amarta_data.get('data', []))}")

        # 2. Mengambil data dealer untuk organisasi vinfast
        print("\n--- Mengambil data dealer untuk vinfast ---")
        vinfast_data = await service.aget_dealer_data("vinfast")
        print(f"Success: {vinfast_data.get('success')}")
        print(f"Total dealers: {len(vinfast_data.get('data', []))}")

        # 3. Test error handling dengan organisasi yang tidak valid
        print("\n--- Test error handling ---")
        try:
            invalid_data = await service.aget_dealer_data("invalid_org")
        except ValueError as e:
            print(f"Validation Error: {e}")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
```

## Endpoint API

Service ini berinteraksi dengan endpoint API:

-   `GET /project/{organization}`
    -   **Parameter Path**: `organization` - Nama organisasi ("vinfast" atau "amarta")
    -   **Response**: JSON object berisi data dealer untuk organisasi yang diminta

## Parameter Organization

### Opsi yang Tersedia:

1. **"amarta"**
   - Mengambil data dealer untuk organisasi Amarta
   - Berisi informasi dealer Honda yang bekerja sama dengan Amarta

2. **"vinfast"**
   - Mengambil data dealer untuk organisasi VinFast
   - Berisi informasi dealer VinFast yang tersedia

### Validasi Input:
- Parameter `organization` harus berupa string
- Nilai harus tepat "amarta" atau "vinfast" (case-sensitive)
- Jika nilai tidak valid, akan menimbulkan `ValueError`

## Error Handling Scenarios

### 1. Invalid Organization Parameter
```python
# Input yang tidak valid
try:
    result = await service.aget_dealer_data("toyota")
except ValueError as e:
    print(f"Error: {e}")  # Output: Organization must be either 'vinfast' or 'amarta'
```

### 2. HTTP Status Errors
```python
# Jika API mengembalikan status error (4xx, 5xx)
try:
    result = await service.aget_dealer_data("amarta")
except httpx.HTTPStatusError as e:
    print(f"HTTP Error {e.response.status_code}: {e.response.text}")
```

### 3. Network/Connection Errors
```python
# Jika terjadi masalah koneksi
try:
    result = await service.aget_dealer_data("amarta")
except httpx.RequestError as e:
    print(f"Request Error: {e}")
```

## Contoh Respon JSON

### `/v1/project/amarta`

```json
{
    "success": true,
    "data": [
        {
            "dealer_id": "D001",
            "dealer_name": "Amarta Honda Bekasi",
            "dealer_code": "AH_BEKASI",
            "address": {
                "street": "Jl. Ahmad Yani No. 123",
                "city": "Bekasi",
                "province": "Jawa Barat",
                "postal_code": "17141"
            },
            "contact": {
                "phone": "+62-21-88888888",
                "email": "<EMAIL>",
                "whatsapp": "+62-812-3456-7890"
            },
            "operating_hours": {
                "weekdays": "08:00-17:00",
                "saturday": "08:00-16:00",
                "sunday": "09:00-15:00"
            },
            "services": [
                "sales",
                "service",
                "spare_parts"
            ],
            "coordinates": {
                "latitude": -6.2088,
                "longitude": 106.8456
            },
            "active": true
        },
        {
            "dealer_id": "D002",
            "dealer_name": "Amarta Honda Jakarta Pusat",
            "dealer_code": "AH_JAKPUS",
            "address": {
                "street": "Jl. Thamrin No. 456",
                "city": "Jakarta Pusat",
                "province": "DKI Jakarta",
                "postal_code": "10230"
            },
            "contact": {
                "phone": "+62-21-77777777",
                "email": "<EMAIL>",
                "whatsapp": "+62-812-9876-5432"
            },
            "operating_hours": {
                "weekdays": "08:00-17:00",
                "saturday": "08:00-16:00",
                "sunday": "closed"
            },
            "services": [
                "sales",
                "service"
            ],
            "coordinates": {
                "latitude": -6.1944,
                "longitude": 106.8229
            },
            "active": true
        }
    ],
    "total_count": 2,
    "organization": "amarta"
}
```

### `/v1/project/vinfast`

```json
{
    "success": true,
    "data": [
        {
            "dealer_id": "VF001",
            "dealer_name": "VinFast Jakarta",
            "dealer_code": "VF_JAKARTA",
            "address": {
                "street": "Jl. Sudirman No. 789",
                "city": "Jakarta Selatan",
                "province": "DKI Jakarta",
                "postal_code": "12190"
            },
            "contact": {
                "phone": "+62-21-99999999",
                "email": "<EMAIL>",
                "whatsapp": "+62-811-1234-5678"
            },
            "operating_hours": {
                "weekdays": "09:00-18:00",
                "saturday": "09:00-17:00",
                "sunday": "10:00-16:00"
            },
            "services": [
                "sales",
                "service",
                "charging_station"
            ],
            "coordinates": {
                "latitude": -6.2297,
                "longitude": 106.8075
            },
            "vehicle_types": [
                "electric_car",
                "electric_motorcycle"
            ],
            "active": true
        }
    ],
    "total_count": 1,
    "organization": "vinfast"
}
```

## Error Response Examples

### Invalid Organization (400 Bad Request)
```json
{
    "success": false,
    "error": {
        "code": "INVALID_ORGANIZATION",
        "message": "Organization parameter must be either 'vinfast' or 'amarta'",
        "details": {
            "provided_value": "toyota",
            "allowed_values": ["vinfast", "amarta"]
        }
    }
}
```

### Organization Not Found (404 Not Found)
```json
{
    "success": false,
    "error": {
        "code": "ORGANIZATION_NOT_FOUND",
        "message": "No dealer data found for the specified organization",
        "details": {
            "organization": "amarta"
        }
    }
}
```

## Penggunaan dalam Tools/Agents

Service ini dapat digunakan dalam tools atau agents untuk:

1. **Pencarian Dealer Terdekat**: Mengambil daftar dealer untuk menentukan lokasi terdekat dengan user
2. **Informasi Kontak Dealer**: Menyediakan informasi kontak dealer untuk customer service
3. **Validasi Dealer**: Memverifikasi keberadaan dan status aktif dealer
4. **Integrasi Peta**: Menggunakan koordinat dealer untuk integrasi dengan layanan peta

## Dependencies

- `httpx`: Untuk HTTP client async
- `typing`: Untuk type hints (Dict, Any)
- `app.services.api_client`: Untuk menggunakan `amarta_api_client`

## Best Practices

1. **Error Handling**: Selalu gunakan try-catch untuk menangani HTTPStatusError dan RequestError
2. **Input Validation**: Validasi parameter organization sebelum melakukan API call
3. **Async/Await**: Gunakan async/await pattern untuk non-blocking operations
4. **Type Hints**: Gunakan type hints untuk better code documentation dan IDE support
5. **Logging**: Pertimbangkan untuk menambahkan logging untuk debugging dan monitoring

## Testing

Untuk menjalankan test yang ada dalam file service:

```bash
# Masuk ke virtual environment terlebih dahulu
source .venv/bin/activate

# Jalankan test
python -m app.services.dealer_service
```

Test akan menampilkan:
- Data dealer untuk organisasi "amarta"
- Data dealer untuk organisasi "vinfast"
- Error handling untuk input yang tidak valid