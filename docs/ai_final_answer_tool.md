# AI Final Answer Tool

## Deskripsi

`ai_final_answer_tool` adalah tool yang digunakan untuk memberikan jawaban final yang komprehensif kepada user. Tool ini berfungsi sebagai penutup percakapan dengan menyajikan informasi yang telah dikumpulkan dalam format yang terstruktur dan mudah dipahami.

## Fitur Utama

- **Jawaban Final Terstruktur**: Memberikan jawaban akhir yang lengkap dan terformat dengan baik
- **Format Output**: Output yang konsisten dengan emoji dan struktur yang jelas
- **State Update**: Mengupdate state `final_answer` dalam AgentBasicState
- **Command Return**: Mengembalikan Command object sesuai dengan dokumentasi LangChain

## Parameter Input

### Required Parameters

- **final_answer** (string): Jawaban final yang akan diberikan kepada user
  - Minimal 10 karakter
  - Harus berisi informasi yang komprehensif

## Format Output

Tool ini mengembalikan Command object yang:
1. Mengupdate state `final_answer` dengan nilai jawaban final
2. Menambahkan ToolMessage ke messages dengan format:

```
📋 **JAWABAN FINAL**
[Jawaban final utama]
```

## Contoh Penggunaan

### Contoh 1: Jawaban Final Sederhana

```python
result = await ai_final_answer_tool.ainvoke({
    "final_answer": "Berdasarkan pencarian, motor Honda BeAT tersedia dalam 3 varian dengan harga mulai dari Rp 18.500.000 di Jakarta."
})
```

### Contoh 2: Jawaban Final untuk Simulasi Kredit

```python
result = await ai_final_answer_tool.ainvoke({
    "final_answer": "Simulasi kredit untuk Honda PCX 160 dengan DP Rp 5.000.000 menghasilkan cicilan bulanan Rp 2.850.000 selama 36 bulan. Disarankan untuk mempertimbangkan asuransi comprehensive dan melakukan test drive sebelum pembelian."
})
```

## Kapan Menggunakan Tool Ini

1. **Setelah Simulasi Kredit**: Memberikan hasil simulasi yang lengkap
2. **Pencarian Informasi Produk**: Merangkum informasi varian motor/mobil
3. **Proses Konsultasi**: Memberikan kesimpulan dari sesi konsultasi
4. **Rekomendasi Produk**: Menyajikan rekomendasi yang sesuai dengan kebutuhan user

## Error Handling

Tool ini akan menghasilkan `ToolException` jika:
- Parameter `final_answer` kosong atau tidak valid
- Terjadi error dalam pemrosesan format output

## Integrasi dengan Tools Lain

Tool ini biasanya digunakan sebagai langkah terakhir setelah menggunakan tools lain seperti:
- `get_variants_tool`: Untuk mendapatkan informasi varian
- `get_credit_simulation_tool`: Untuk simulasi kredit
- `planning_tool`: Untuk eksekusi rencana

## File Location

```
app/tools/ai_final_answer_tool.py
```

## Dependencies

- `langchain_core.tools.StructuredTool`
- `langchain_core.tools.InjectedToolCallId`
- `langchain_core.messages.ToolMessage`
- `langchain_core.runnables.RunnableConfig`
- `langgraph.types.Command`
- `pydantic.BaseModel`
- `typing.Optional`
- `typing_extensions.Annotated`