# Get Credit Simulation Tool and Promo

Tool ini digunakan untuk mengambil dan memproses data simulasi kredit motor Honda beserta informasi promo yang tersedia. Tool ini menggabungkan data dari `CreditSimulationService` dan `GetPromoService` untuk memberikan informasi kredit yang komprehensif kepada pengguna.

## Fungsi Utama

- **Simulasi Kredit**: Mengambil data simulasi kredit untuk model dan varian motor tertentu di area geografis yang spesifik.
- **Integrasi Promo**: Menggabungkan data promo yang relevan dengan simulasi kredit reguler.
- **Dual Mode**: Mendukung dua mode operasi - `get_summary` untuk ringkasan dan `get_simulation` untuk data detail.
- **Auto-Detection**: Secara otomatis mendeteksi mode yang tepat berdasarkan parameter yang diberikan.
- **Filtering**: Menyaring hasil simulasi berdasarkan kriteria rentang yang ditentukan pengguna (DP, tenor, cicilan).
- **Validasi Input**: Memastikan parameter yang diberikan oleh pengguna (seperti rentang DP atau tenor) sesuai dengan aturan bisnis yang telah ditetapkan.

## Komponen

Tool ini terdiri dari beberapa bagian utama yang bekerja sama:

1.  **`get_credit_simulation_and_promo_tool` (StructuredTool)**
    *   Objek `StructuredTool` dari LangChain yang mendefinisikan nama, deskripsi, skema argumen, dan fungsi korutin untuk dieksekusi.
    *   Merupakan antarmuka utama yang dipanggil oleh AI agent.

2.  **`CreditSimulationSchema` (Pydantic BaseModel)**
    *   Mendefinisikan skema input untuk tool, termasuk semua parameter yang dapat diterima seperti `city_group`, `model_name`, `variant_code`, dan filter rentang (DP, tenor, cicilan).
    *   Menyediakan validasi tipe data dan deskripsi untuk setiap field, membantu LLM dalam menyusun pemanggilan tool yang benar.

3.  **`_aget_credit_simulation_tool` (Coroutine Function)**
    *   Fungsi inti yang berisi semua logika bisnis.
    *   Mengorkestrasi pemanggilan ke `CreditSimulationService` dan `GetPromoService`.
    *   Melakukan auto-deteksi mode jika tidak ditentukan secara eksplisit.
    *   Memproses dan menggabungkan data simulasi dan promo.
    *   Memanggil fungsi helper untuk memformat output sesuai mode yang dipilih.

4.  **Fungsi Helper Internal**
    *   `_validate_range_parameters`: Memvalidasi aturan bisnis untuk parameter rentang.
    *   `_track_available_options`: Mengidentifikasi rentang minimum dan maksimum dari data simulasi.
    *   `_generate_summary_output`: Memformat output untuk mode `get_summary`.
    *   `_generate_promo_summary`: Memformat ringkasan promo yang mudah dibaca.
    *   `_extract_promo_credit_schemes`: Mengubah data kredit dari promo menjadi format simulasi standar.
    *   `_filter_simulations`: Menyaring data simulasi berdasarkan kriteria rentang yang diberikan pengguna dan memformatnya ke dalam CSV.

## Alur Kerja

1.  **Pemanggilan Tool**: AI agent memanggil tool `get_credit_simulation_and_promo` berdasarkan permintaan pengguna (misalnya, "Berapa cicilan Vario 125 di Bandung dengan DP 3 juta?").
2.  **Auto-Deteksi Mode**: Jika parameter `mode` tidak diisi, tool akan mendeteksinya secara otomatis. Jika ada parameter rentang (seperti `min_dp`, `max_tenor`), mode akan diatur ke `get_simulation`. Jika tidak, mode diatur ke `get_summary`.
3.  **Pengambilan Data**:
    *   Tool memanggil `CreditSimulationService` untuk mendapatkan data simulasi kredit dasar.
    *   Jika `include_promo` adalah `True` (default), tool juga memanggil `GetPromoService` untuk mengambil semua promo yang relevan untuk model dan varian tersebut.
4.  **Pemrosesan Berdasarkan Mode**:
    *   **Mode `get_summary`**:
        *   Tool menggabungkan data simulasi reguler dan data kredit dari promo.
        *   Fungsi `_generate_summary_output` dipanggil untuk menghasilkan ringkasan yang berisi nilai minimum dan maksimum untuk DP, tenor, dan cicilan.
        *   Jika ada data promo, `_generate_promo_summary` juga dipanggil untuk membuat ringkasan promo.
        *   Hasil akhir adalah gabungan dari ringkasan kredit dan ringkasan promo, dipisahkan oleh `=== PROMO TERSEDIA ===`.
    *   **Mode `get_simulation`**:
        *   Tool menggabungkan data simulasi reguler dan data kredit dari promo.
        *   Fungsi `_filter_simulations` menyaring semua data berdasarkan kriteria rentang yang diberikan pengguna (misalnya, `min_dp`, `max_dp`).
        *   Hasil yang cocok diformat ke dalam string CSV.
        *   Jika ada data promo, `_generate_promo_summary` dipanggil untuk membuat detail promo.
        *   Hasil akhir adalah string CSV dari simulasi yang cocok, diikuti oleh detail promo di bawah `=== DETAIL PROMO ===`.
5.  **Pengembalian Hasil**: Hasil yang telah diformat (baik ringkasan atau CSV) dikembalikan ke AI agent untuk disampaikan kepada pengguna.

## Mode Operasi

Tool ini memiliki dua mode operasi utama:

### 1. `get_summary`
- **Tujuan**: Memberikan gambaran umum tentang opsi kredit yang tersedia tanpa filter spesifik.
- **Kapan digunakan**: Ketika pengguna bertanya tentang rentang umum, seperti "DP paling kecil berapa?" atau "Tenor paling lama berapa tahun?".
- **Output**: Teks ringkasan yang berisi:
    - Min/Max DP
    - Min/Max Tenor
    - Min/Max Cicilan
    - Ringkasan promo yang tersedia (jika ada).

#### Contoh Respons Mode `get_summary`

```
Ringkasan Kredit Honda BeAT di Jakarta:

DP (Down Payment):
- Minimum: Rp 2.500.000
- Maksimum: Rp 15.000.000

Tenor:
- Minimum: 12 bulan (1 tahun)
- Maksimum: 60 bulan (5 tahun)

Cicilan Bulanan:
- Minimum: Rp 850.000
- Maksimum: Rp 3.200.000

=== PROMO TERSEDIA ===

Promo Spesial Honda BeAT:
- Nama: Promo Akhir Tahun BeAT
- Periode: 1 Desember 2023 - 31 Januari 2024
- Benefit: Cicilan mulai Rp 799.000/bulan
- Syarat: DP minimal Rp 3.000.000, tenor 24-36 bulan
- Lokasi: Jakarta, Bogor, Depok, Tangerang, Bekasi
```

### 2. `get_simulation`
- **Tujuan**: Memberikan daftar skema kredit yang spesifik setelah disaring berdasarkan kriteria pengguna.
- **Kapan digunakan**: Ketika pengguna memberikan kriteria jelas, seperti "DP 3 jutaan", "tenor 2 tahun", atau "cicilan di bawah 1,5 juta".
- **Output**:
    - String berformat **CSV** dengan header `dp,tenor,installment,is_promo`.
    - Detail promo yang relevan (jika ada).

#### Contoh Respons Mode `get_simulation`

```
dp,tenor,installment,is_promo
3000000,24,1250000,false
3500000,24,1180000,false
3000000,36,950000,false
3500000,36,890000,false
3000000,24,799000,true
3500000,24,750000,true

=== DETAIL PROMO ===

Promo Spesial Honda BeAT:
- Nama: Promo Akhir Tahun BeAT
- Periode: 1 Desember 2023 - 31 Januari 2024
- Benefit: Cicilan mulai Rp 799.000/bulan
- Syarat: DP minimal Rp 3.000.000, tenor 24-36 bulan
- Lokasi: Jakarta, Bogor, Depok, Tangerang, Bekasi
```

#### Contoh Respons Ketika Tidak Ada Data

```
Maaf, tidak ada skema kredit yang cocok dengan kriteria Anda.

Opsi yang tersedia untuk Honda BeAT di Jakarta:
- DP: Rp 2.500.000 - Rp 15.000.000
- Tenor: 12 - 60 bulan
- Cicilan: Rp 850.000 - Rp 3.200.000

Silakan coba dengan kriteria yang berbeda.
```

## Parameter Input (`CreditSimulationSchema`)

| Parameter | Tipe | Deskripsi | Contoh |
| :--- | :--- | :--- | :--- |
| `city_group` | `str` | Grup kota (lowercase). **Wajib** diisi dan harus sama dengan yang digunakan di `get_variants`. | `"bandung"`, `"jakarta"` |
| `model_name` | `str` | Nama model motor. **Wajib** diisi. | `"BeAT"`, `"Vario 125"` |
| `variant_code` | `str` | Kode varian yang didapat dari `get_variants_tool`. **Wajib** diisi. | `"H1B02N41L1A/T"` |
| `mode` | `CreditSimulationMode` | Mode operasi: `get_summary` atau `get_simulation`. Auto-detect jika tidak diisi. | `get_summary` |
| `min_dp` | `Optional[int]` | Minimum DP dalam Rupiah. Untuk query "DP minimal X" atau "DP mulai dari X". | `3000000` |
| `max_dp` | `Optional[int]` | Maksimum DP dalam Rupiah. Untuk query "DP di bawah X". | `3999999` |
| `min_tenor` | `Optional[int]` | Minimum tenor dalam bulan. | `24` |
| `max_tenor` | `Optional[int]` | Maksimum tenor dalam bulan. | `35` |
| `min_installment` | `Optional[int]` | Minimum cicilan bulanan dalam Rupiah. | `1000000` |
| `max_installment` | `Optional[int]` | Maksimum cicilan bulanan dalam Rupiah. | `1499999` |
| `include_promo` | `Optional[bool]` | Apakah akan mengambil data promo. Defaultnya `True`. | `True` |

## Aturan Validasi Parameter

Tool ini menerapkan aturan validasi berikut untuk parameter rentang:

1.  **DP (Down Payment)**:
    *   `min_dp` harus selalu kurang dari `max_dp`
    *   Rentang DP harus memiliki selisih minimal 999.999

2.  **Tenor**:
    *   `min_tenor` harus selalu kurang dari `max_tenor`
    *   Rentang tenor harus memiliki selisih minimal 11 bulan (satu tahun dikurangi satu)

3.  **Installment (Cicilan)**:
    *   `min_installment` harus selalu kurang dari `max_installment`
    *   Rentang installment harus memiliki selisih minimal 999.999

## Panduan Penggunaan Parameter

- Gunakan mode 'get_summary' untuk informasi range (DP paling kecil/besar, tenor min/max)
- Gunakan mode 'get_simulation' untuk simulasi dengan kriteria range tertentu
- Jika mode tidak disediakan, maka akan auto-detect berdasarkan adanya parameter range
- Parameter variant_code harus didapat dari tool get_variants terlebih dahulu
- min_dp harus < max_dp, selisih minimal harus Rp 999.999 apapun kondisinya.
- min_tenor harus < max_tenor, selisih minimal harus 11 bulan apapun kondisinya.
- min_installment harus < max_installment, selisih minimal harus Rp 999.999 apapun kondisinya.
- Jika user menyebut "di bawah X", maka gunakan max_only (min=None).
- Jika user menyebut "minimal", "di atas X", maka gunakan min_only (max=None).
- Jika user menyebut "X tahun", terjemahkan ke bulan: X * 12 - 1.
- Jika user menyebut "tenor di bawah X tahun", maka max_tenor = (X * 12) - 1

## Contoh Penggunaan Parameter

Berikut adalah beberapa contoh penggunaan parameter untuk kasus umum:

### DP (Down Payment)
- min_dp=3000000, max_dp=3999999: DP 3 juta
- min_dp=5000000, max_dp=7999999: DP 5 juta 
- min_dp=10000000, max_dp=14999999: DP 10 juta
- min_dp=15000000, max_dp=19999999: DP 15 juta
- min_dp=20000000, max_dp=24999999: DP 20 juta

### Tenor
- min_tenor=23, max_tenor=34: Tenor 2 tahun
- min_tenor=35, max_tenor=46: Tenor 3 tahun
- min_tenor=47, max_tenor=58: Tenor 4 tahun
- min_tenor=59, max_tenor=70: Tenor 5 tahun
- min_tenor=71, max_tenor=82: Tenor 6 tahun

### Installment (Cicilan)
- min_installment=1000000, max_installment=1999999: Cicilan 1 juta
- min_installment=1500000, max_installment=2499999: Cicilan 1,5 juta
- min_installment=2000000, max_installment=2999999: Cicilan 2 juta
- min_installment=2500000, max_installment=3499999: Cicilan 2,5 juta
- min_installment=3000000, max_installment=3999999: Cicilan 3 juta

## Contoh Pemanggilan Tool (Summary)
```python
# Pertanyaan: "Untuk Scoopy di Cirebon, pilihan DP dan tenornya apa saja?"
result = await get_credit_simulation_and_promo_tool.ainvoke({
    "city_group": "cirebon",
    "model_name": "scoopy",
    "variant_code": "F1C02N46L2A/T",
    "mode": "get_summary"
})
print(result)
```

## Contoh Pemanggilan Tool (Simulasi)
```python
# Pertanyaan: "Saya mau simulasi kredit Scoopy di Cirebon, DP 3 jutaan, tenor 3 tahun."
result = await get_credit_simulation_and_promo_tool.ainvoke({
    "city_group": "cirebon",
    "model_name": "scoopy",
    "variant_code": "F1C02N46L2A/T",
    "min_dp": 3000000,
    "max_dp": 3999999,
    "min_tenor": 35,
    "max_tenor": 35,
    "include_promo": True
})
print(result)
```

## Contoh Pertanyaan Pengguna

Tool ini dirancang untuk menangani pertanyaan seperti:
- "Berapa cicilan motor BeAT di Jakarta dengan DP 3 juta?"
- "Simulasi kredit Vario 125 tenor 2 tahun di Bandung"
- "Angsuran PCX dengan DP minimal 5 juta"
- "Cicilan motor Scoopy dibawah 1,5 juta per bulan"
- "Range DP dan tenor untuk motor BeAT di Surabaya"
- "Simulasi kredit motor dengan DP 4-6 juta"
- "Berapa cicilan motor dengan tenor dibawah 3 tahun?"
- "DP paling kecil untuk Honda BeAT di Jakarta?"
- "Tenor paling lama berapa tahun untuk motor Honda?"
- "Angsuran 1 jutaan untuk Honda Vario di Medan"

## Ketergantungan

Tool ini bergantung pada dua service utama:
1.  **`CreditSimulationService`**: Untuk mendapatkan data simulasi kredit dasar. Lihat dokumentasi [CreditSimulationService](./credit_simulation_service.md).
2.  **`GetPromoService`**: Untuk mendapatkan data promo yang relevan. Lihat dokumentasi [GetPromoService](./get_promo_service.md).

## Penanganan Error

Tool ini memiliki mekanisme penanganan error yang informatif:
- **`ToolException`**:
    - Jika parameter wajib tidak diisi (misalnya `city_group`).
    - Jika parameter rentang tidak valid (misalnya `min_dp` >= `max_dp`).
    - Jika API simulasi mengembalikan error 404, yang berarti tidak ada skema kredit untuk varian di area tersebut.
    - Jika mode 'get_simulation' digunakan tanpa parameter range.
- **`NotAvailableCreditScheme`**:
    - Exception kustom yang muncul jika tidak ada skema kredit yang cocok dengan kriteria filter pengguna.
    - Pesan error yang dikembalikan akan menyertakan ringkasan opsi yang *tersedia* (rentang DP, tenor, dan cicilan yang ada) untuk membantu pengguna memperbaiki query mereka.