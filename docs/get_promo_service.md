# Get Promo Service

## Deskripsi

Service `GetPromoService` bertanggung jawab untuk mengambil informasi mengenai promo dari API Amarta. Service ini menyediakan beberapa metode untuk mendapatkan data promo berdasarkan kriteria yang berbeda, seperti ID sumber iklan, model dan varian kend<PERSON>, atau kode promo spesifik.

## Komponen

Service ini didefinisikan dalam `app/services/get_promo_service.py` dan memiliki beberapa metode utama:

1.  **`aget_promo_from_ad_source_id(source_id)`**
    *   **Tujuan**: Mengambil data promo berdasarkan `source_id` yang biasanya didapat dari iklan.
    *   **Argumen**: `source_id` (string) - ID unik dari sumber iklan.
    *   **Endpoint**: `/deal/all/sourceid`

2.  **`aget_promo_by_model_and_variant(model, variant_code)`**
    *   **Tujuan**: Mengambil data promo yang berlaku untuk model dan varian kendaraan tertentu.
    *   **Argumen**:
        *   `model` (string) - <PERSON>a model kendaraan (contoh: "scoopy").
        *   `variant_code` (string) - Kode unik untuk varian spesifik.
    *   **Endpoint**: `/deal/amarta`

3.  **`aget_promo_by_code(code)`**
    *   **Tujuan**: Mengambil detail promo menggunakan kode promo yang spesifik.
    *   **Argumen**: `code` (string) - Kode promo (contoh: "SCOOPYDP800CRB").
    *   **Endpoint**: `/deal/all/code`

## Alur Kerja

1.  Komponen lain (biasanya sebuah *tool*) yang membutuhkan informasi promo akan memanggil salah satu metode dari `GetPromoService`.
2.  Metode yang dipanggil akan membuat permintaan (request) ke endpoint API Amarta yang sesuai dengan parameter yang diberikan.
3.  Service akan menerima respons dalam format JSON dari API.
4.  Jika permintaan tidak berhasil, sebuah `HTTPStatusError` akan dimunculkan.
5.  Jika berhasil, data JSON yang berisi detail promo akan dikembalikan ke pemanggil.

## Contoh Penggunaan

```python
import asyncio
from app.services.get_promo_service import GetPromoService


async def main():
    service = GetPromoService()

    try:
        # 1. Mengambil promo berdasarkan source ID
        print("--- Mengambil promo berdasarkan source ID ---")
        promo_data_source = await service.aget_promo_from_ad_source_id("6730637160071")
        print(f"Caption: {promo_data_source.get('data', {}).get('caption')}")

        # 2. Mengambil promo berdasarkan model dan varian
        print("\n--- Mengambil promo berdasarkan model dan varian ---")
        promo_data_model = await service.aget_promo_by_model_and_variant(
            model_name="scoopy", variant_code="F1C02N46L2A/T"
        )
        print(f"Total Data: {len(promo_data_model.get('data', []))}")

        # 3. Mengambil promo berdasarkan kode
        print("\n--- Mengambil promo berdasarkan kode ---")
        promo_data_code = await service.aget_promo_by_code("SCOOPYDP800CRB")
        print(f"Caption: {promo_data_code.get('data', {}).get('caption')}")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
```

## Endpoint API

Service ini berinteraksi dengan beberapa endpoint API:

-   `GET /deal/all/sourceid`
    -   **Parameter**: `source_id`
-   `GET /deal/amarta`
    -   **Parameter**: `ai`, `model`, `variant_code`
-   `GET /deal/all/code`
    -   **Parameter**: `code`

## Contoh Respon JSON

### `/v1/deal/all/sourceid?source_id=6730637160071`

```json
{
    "success": true,
    "data": {
        "company": "AMARTA",
        "deal_code": "SCOOPYDP800BKS",
        "url_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100.png",
        "url_thumb_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100-thumb.png",
        "start_period": "2025-07-21T00:00:00.000",
        "end_period": "2025-08-31T23:59:59.000",
        "active": true,
        "show": false,
        "caption": "SCOOPY DP 800 Khusus KTP BEKASI",
        "notes": "<p>*SCOOPY ENERGETIC*<br>Harga: Rp23.250.000 <br>Area: BEKASI <br><br>promo scoopy kunci dp 800rb 35x angs 1.039.000</p>\n<p>Untuk informasi lebih detail tentang spesifikasi, fitur, dan penawaran khusus, silakan kunjungi link berikut:<br>https://amartahonda.com/baru/BEKASI/F1C02N46L2AT</p>",
        "purchase_method": "credit",
        "area": [
            "bekasi"
        ],
        "default_address": {
            "city_code": "32.75",
            "city_name": "kota bekasi",
            "province_code": "32",
            "province_name": "jawa barat"
        },
        "custom_price": 0,
        "custom_price_range_up": 0,
        "custom_price_range_down": 0,
        "take_vehicle_in_dealer": "false",
        "agent_code": "",
        "promo_codes": [
            "JABARSCOOPY25L"
        ],
        "total_promo_discount": 2200000,
        "vehicle": {
            "variant_custom": [
                {
                    "code": "F1C02N46L2A/T|MH",
                    "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                    "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                    "variant_name": "SCOOPY ENERGETIC",
                    "variant_color_code": "MH",
                    "variant_code": "F1C02N46L2A/T",
                    "variant_color_name": "merah hitam"
                },
                {
                    "code": "F1C02N46L2A/T|SH",
                    "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                    "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                    "variant_name": "SCOOPY ENERGETIC",
                    "variant_color_code": "SH",
                    "variant_code": "F1C02N46L2A/T",
                    "variant_color_name": "silver hitam"
                }
            ],
            "variant_alternative_color": {
                "name": "warna sesuai pesanan",
                "code": "SAME_AS_ORDER"
            },
            "model_name": "Scoopy",
            "models_name": [
                "scoopy"
            ]
        },
        "credit": [
            {
                "dp_amount": 800000,
                "tenor": [
                    "35"
                ],
                "installment_amount": 1039000,
                "finco_name": "PT FEDERAL INTERNATIONAL FINANCE",
                "finco_branch": "",
                "installment_amount_strikethrough": 0,
                "finco_code": "FIFG",
                "otr": 0,
                "tenor_strikethrough": [
                    null
                ]
            },
            {
                "dp_amount": 800000,
                "tenor": [
                    "35"
                ],
                "installment_amount": 1039000,
                "finco_name": "PT OTO FINANCE",
                "finco_branch": "",
                "installment_amount_strikethrough": 0,
                "finco_code": "OTOF",
                "otr": 0,
                "tenor_strikethrough": [
                    null
                ]
            }
        ],
        "data_entry_option": [],
        "image_campaign_custom": [],
        "event_points": [
            {
                "name": "melengkapi data pemilik",
                "key": "contact",
                "point": 2
            },
            {
                "name": "melengkapi data pemilik + wa verify",
                "key": "contact_wa_verify",
                "point": 5
            },
            {
                "name": "melengkapi data ktp",
                "key": "id_card",
                "point": 3
            },
            {
                "name": "melengkapi data kk",
                "key": "family_card",
                "point": 3
            },
            {
                "name": "melengkapi image selfie",
                "key": "selfie",
                "point": 3
            },
            {
                "name": "kirim aplikasi",
                "key": "send_application",
                "point": 3
            }
        ],
        "default_finco_id": "6730637160071",
        "default_dealer_code": "",
        "default_dealer_name": "",
        "ideal_conversation_flow": "6730637160071",
        "ads_parameter": [],
        "gross_return_data": 0,
        "source_id": "6730637160071",
        "deal_type": "DEAL"
    }
}
```

### `/v1/deal/amarta?ai=true&model=scoopy&variant_code=F1C02N46L2A/T`

```json
{
    "success": true,
    "data": [
        {
            "company": "AMARTA",
            "deal_code": "SCOOPYDP800CRB",
            "url_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100.png",
            "url_thumb_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100-thumb.png",
            "start_period": "2025-07-21T00:00:00.000",
            "end_period": "2025-08-31T23:59:59.000",
            "active": true,
            "show": false,
            "caption": "SCOOPY DP 800 Khusus KTP",
            "notes": "<p>PROMO SCOOPY CIREBON</p>\n<p>*Scoopy Kunci ( Fashion &amp; Energetic )*</p>\n<p>Harga: Rp23.300.000</p>\n<p>Area: CIREBON<br><br>promo scoopy kunci dp 800rb 35x angs 1.067.000</p>",
            "purchase_method": "credit",
            "area": [
                "cirebon"
            ],
            "default_address": {
                "city_code": "32.74",
                "city_name": "kota cirebon",
                "province_code": "32",
                "province_name": "jawa barat"
            },
            "custom_price": 0,
            "custom_price_range_up": 0,
            "custom_price_range_down": 0,
            "take_vehicle_in_dealer": "false",
            "agent_code": "",
            "promo_codes": [
                "JABARSCOOPY25L"
            ],
            "total_promo_discount": 2200000,
            "vehicle": {
                "variant_custom": [
                    {
                        "code": "F1C02N46L2A/T|MH",
                        "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                        "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                        "variant_name": "SCOOPY ENERGETIC",
                        "variant_color_code": "MH",
                        "variant_code": "F1C02N46L2A/T",
                        "variant_color_name": "merah hitam"
                    },
                    {
                        "code": "F1C02N46L2A/T|SH",
                        "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                        "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                        "variant_name": "SCOOPY ENERGETIC",
                        "variant_color_code": "SH",
                        "variant_code": "F1C02N46L2A/T",
                        "variant_color_name": "silver hitam"
                    }
                ],
                "variant_alternative_color": {
                    "name": "warna sesuai pesanan",
                    "code": "SAME_AS_ORDER"
                },
                "model_name": "Scoopy",
                "models_name": [
                    "scoopy"
                ]
            },
            "credit": [
                {
                    "dp_amount": 800000,
                    "tenor": [
                        "35"
                    ],
                    "installment_amount": 1067000,
                    "finco_name": "PT FEDERAL INTERNATIONAL FINANCE",
                    "finco_branch": "",
                    "installment_amount_strikethrough": 0,
                    "finco_code": "FIFG",
                    "otr": 0,
                    "tenor_strikethrough": [
                        null
                    ]
                }
            ],
            "data_entry_option": [],
            "image_campaign_custom": [],
            "event_points": [
                {
                    "name": "melengkapi data pemilik",
                    "key": "contact",
                    "point": 2
                },
                {
                    "name": "melengkapi data pemilik + wa verify",
                    "key": "contact_wa_verify",
                    "point": 5
                },
                {
                    "name": "melengkapi data ktp",
                    "key": "id_card",
                    "point": 3
                },
                {
                    "name": "melengkapi data kk",
                    "key": "family_card",
                    "point": 3
                },
                {
                    "name": "melengkapi image selfie",
                    "key": "selfie",
                    "point": 3
                },
                {
                    "name": "kirim aplikasi",
                    "key": "send_application",
                    "point": 3
                }
            ],
            "default_finco_id": "6745414790871",
            "default_dealer_code": "",
            "default_dealer_name": "",
            "ideal_conversation_flow": "6745414790871",
            "ads_parameter": [],
            "gross_return_data": 0,
            "source_id": "6745414790871",
            "create_time": "2025-07-29T13:01:17.404",
            "deal_type": "DEAL"
        },
        {
            "company": "6745414791271",
            "deal_code": "SCOOPYDP800",
            "url_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100.png",
            "url_thumb_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100-thumb.png",
            "start_period": "2025-07-21T00:00:00.000",
            "end_period": "2025-08-31T23:59:59.000",
            "active": true,
            "show": false,
            "caption": "SCOOPY DP 800",
            "notes": "<p>PROMO SCOOPY BANDUNG</p>\n<p>*Scoopy Kunci ( Fashion &amp; Energetic )*</p>\n<p>Harga: Rp23.300.000</p>\n<p>Area: BANDUNG<br><br>promo scoopy kunci dp 800rb 35x angs 1.063.000</p>\n<p>*Scoopy Remote ( Prestige &amp; Stylish )*</p>\n<p>Dp 3.100.000</p>\n<p>Diskon 2.200.000</p>\n<p>Dp Bayar 900.000</p>\n<p>Angsuran 1.098.000 x 33</p>",
            "purchase_method": "credit",
            "area": [
                "bandung"
            ],
            "default_address": {
                "city_code": "32.73",
                "city_name": "kota bandung",
                "province_code": "32",
                "province_name": "jawa barat"
            },
            "custom_price": 0,
            "custom_price_range_up": 0,
            "custom_price_range_down": 0,
            "take_vehicle_in_dealer": "false",
            "agent_code": "",
            "promo_codes": [
                "JABARSCOOPY25L"
            ],
            "total_promo_discount": 2200000,
            "vehicle": {
                "variant_custom": [
                    {
                        "code": "F1C02N46L2A/T|MH",
                        "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                        "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                        "variant_name": "SCOOPY ENERGETIC",
                        "variant_color_code": "MH",
                        "variant_code": "F1C02N46L2A/T",
                        "variant_color_name": "merah hitam"
                    },
                    {
                        "code": "F1C02N46L2A/T|SH",
                        "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                        "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                        "variant_name": "SCOOPY ENERGETIC",
                        "variant_color_code": "SH",
                        "variant_code": "F1C02N46L2A/T",
                        "variant_color_name": "silver hitam"
                    }
                ],
                "variant_alternative_color": {
                    "name": "warna sesuai pesanan",
                    "code": "SAME_AS_ORDER"
                },
                "model_name": "Scoopy",
                "models_name": [
                    "scoopy"
                ]
            },
            "credit": [
                {
                    "dp_amount": 800000,
                    "tenor": [
                        "33"
                    ],
                    "installment_amount": 1063000,
                    "finco_name": "PT FEDERAL INTERNATIONAL FINANCE",
                    "finco_branch": "",
                    "installment_amount_strikethrough": 0,
                    "finco_code": "FIFG",
                    "otr": 0,
                    "tenor_strikethrough": [
                        null
                    ]
                }
            ],
            "data_entry_option": [],
            "image_campaign_custom": [],
            "event_points": [
                {
                    "name": "melengkapi data pemilik",
                    "key": "contact",
                    "point": 2
                },
                {
                    "name": "melengkapi data pemilik + wa verify",
                    "key": "contact_wa_verify",
                    "point": 5
                },
                {
                    "name": "melengkapi data ktp",
                    "key": "id_card",
                    "point": 3
                },
                {
                    "name": "melengkapi data kk",
                    "key": "family_card",
                    "point": 3
                },
                {
                    "name": "melengkapi image selfie",
                    "key": "selfie",
                    "point": 3
                },
                {
                    "name": "kirim aplikasi",
                    "key": "send_application",
                    "point": 3
                }
            ],
            "default_finco_id": "6745414791271",
            "default_dealer_code": "",
            "default_dealer_name": "",
            "ideal_conversation_flow": "6745414791271",
            "ads_parameter": [],
            "gross_return_data": 0,
            "source_id": "6745414791271",
            "create_time": "2025-07-29T12:54:28.367",
            "deal_type": "DEAL"
        },
        {
            "company": "AMARTA",
            "deal_code": "SCOOPYDP800BKS",
            "url_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100.png",
            "url_thumb_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100-thumb.png",
            "start_period": "2025-07-21T00:00:00.000",
            "end_period": "2025-08-31T23:59:59.000",
            "active": true,
            "show": false,
            "caption": "SCOOPY DP 800 Khusus KTP BEKASI",
            "notes": "<p>*SCOOPY ENERGETIC*<br>Harga: Rp23.250.000 <br>Area: BEKASI <br><br>promo scoopy kunci dp 800rb 35x angs 1.039.000</p>\n<p>Untuk informasi lebih detail tentang spesifikasi, fitur, dan penawaran khusus, silakan kunjungi link berikut:<br>https://amartahonda.com/baru/BEKASI/F1C02N46L2AT</p>",
            "purchase_method": "credit",
            "area": [
                "bekasi"
            ],
            "default_address": {
                "city_code": "32.75",
                "city_name": "kota bekasi",
                "province_code": "32",
                "province_name": "jawa barat"
            },
            "custom_price": 0,
            "custom_price_range_up": 0,
            "custom_price_range_down": 0,
            "take_vehicle_in_dealer": "false",
            "agent_code": "",
            "promo_codes": [
                "JABARSCOOPY25L"
            ],
            "total_promo_discount": 2200000,
            "vehicle": {
                "variant_custom": [
                    {
                        "code": "F1C02N46L2A/T|MH",
                        "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                        "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                        "variant_name": "SCOOPY ENERGETIC",
                        "variant_color_code": "MH",
                        "variant_code": "F1C02N46L2A/T",
                        "variant_color_name": "merah hitam"
                    },
                    {
                        "code": "F1C02N46L2A/T|SH",
                        "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                        "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                        "variant_name": "SCOOPY ENERGETIC",
                        "variant_color_code": "SH",
                        "variant_code": "F1C02N46L2A/T",
                        "variant_color_name": "silver hitam"
                    }
                ],
                "variant_alternative_color": {
                    "name": "warna sesuai pesanan",
                    "code": "SAME_AS_ORDER"
                },
                "model_name": "Scoopy",
                "models_name": [
                    "scoopy"
                ]
            },
            "credit": [
                {
                    "dp_amount": 800000,
                    "tenor": [
                        "35"
                    ],
                    "installment_amount": 1039000,
                    "finco_name": "PT FEDERAL INTERNATIONAL FINANCE",
                    "finco_branch": "",
                    "installment_amount_strikethrough": 0,
                    "finco_code": "FIFG",
                    "otr": 0,
                    "tenor_strikethrough": [
                        null
                    ]
                },
                {
                    "dp_amount": 800000,
                    "tenor": [
                        "35"
                    ],
                    "installment_amount": 1039000,
                    "finco_name": "PT OTO FINANCE",
                    "finco_branch": "",
                    "installment_amount_strikethrough": 0,
                    "finco_code": "OTOF",
                    "otr": 0,
                    "tenor_strikethrough": [
                        null
                    ]
                }
            ],
            "data_entry_option": [],
            "image_campaign_custom": [],
            "event_points": [
                {
                    "name": "melengkapi data pemilik",
                    "key": "contact",
                    "point": 2
                },
                {
                    "name": "melengkapi data pemilik + wa verify",
                    "key": "contact_wa_verify",
                    "point": 5
                },
                {
                    "name": "melengkapi data ktp",
                    "key": "id_card",
                    "point": 3
                },
                {
                    "name": "melengkapi data kk",
                    "key": "family_card",
                    "point": 3
                },
                {
                    "name": "melengkapi image selfie",
                    "key": "selfie",
                    "point": 3
                },
                {
                    "name": "kirim aplikasi",
                    "key": "send_application",
                    "point": 3
                }
            ],
            "default_finco_id": "6730637160071",
            "default_dealer_code": "",
            "default_dealer_name": "",
            "ideal_conversation_flow": "6730637160071",
            "ads_parameter": [],
            "gross_return_data": 0,
            "source_id": "6730637160071",
            "create_time": "2025-07-28T10:01:16.863",
            "deal_type": "DEAL"
        }
    ]
}
```

### `/deal/all/code?code=SCOOPYDP800CRB`

```json
{
    "success": true,
    "data": {
        "company": "AMARTA",
        "deal_code": "SCOOPYDP800BKS",
        "url_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100.png",
        "url_thumb_image": "https://cdn.trimitra.biz/ZG9uaUBpbnZlbnRvLnRlY2hub2xvZ3k%3D/ART-16654830664100-thumb.png",
        "start_period": "2025-07-21T00:00:00.000",
        "end_period": "2025-08-31T23:59:59.000",
        "active": true,
        "show": false,
        "caption": "SCOOPY DP 800 Khusus KTP BEKASI",
        "notes": "<p>*SCOOPY ENERGETIC*<br>Harga: Rp23.250.000 <br>Area: BEKASI <br><br>promo scoopy kunci dp 800rb 35x angs 1.039.000</p>\n<p>Untuk informasi lebih detail tentang spesifikasi, fitur, dan penawaran khusus, silakan kunjungi link berikut:<br>https://amartahonda.com/baru/BEKASI/F1C02N46L2AT</p>",
        "purchase_method": "credit",
        "area": [
            "bekasi"
        ],
        "default_address": {
            "city_code": "32.75",
            "city_name": "kota bekasi",
            "province_code": "32",
            "province_name": "jawa barat"
        },
        "custom_price": 0,
        "custom_price_range_up": 0,
        "custom_price_range_down": 0,
        "take_vehicle_in_dealer": "false",
        "agent_code": "",
        "promo_codes": [
            "JABARSCOOPY25L"
        ],
        "total_promo_discount": 2200000,
        "vehicle": {
            "variant_custom": [
                {
                    "code": "F1C02N46L2A/T|MH",
                    "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                    "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309138994350.png",
                    "variant_name": "SCOOPY ENERGETIC",
                    "variant_color_code": "MH",
                    "variant_code": "F1C02N46L2A/T",
                    "variant_color_name": "merah hitam"
                },
                {
                    "code": "F1C02N46L2A/T|SH",
                    "custom_thumb_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                    "custom_image": "https://cdn.trimitra.biz/YXV0b0B0cmltaXRyYS5iaXo%3D/ART-17309139047226.png",
                    "variant_name": "SCOOPY ENERGETIC",
                    "variant_color_code": "SH",
                    "variant_code": "F1C02N46L2A/T",
                    "variant_color_name": "silver hitam"
                }
            ],
            "variant_alternative_color": {
                "name": "warna sesuai pesanan",
                "code": "SAME_AS_ORDER"
            },
            "model_name": "Scoopy",
            "models_name": [
                "scoopy"
            ]
        },
        "credit": [
            {
                "dp_amount": 800000,
                "tenor": [
                    "35"
                ],
                "installment_amount": 1039000,
                "finco_name": "PT FEDERAL INTERNATIONAL FINANCE",
                "finco_branch": "",
                "installment_amount_strikethrough": 0,
                "finco_code": "FIFG",
                "otr": 0,
                "tenor_strikethrough": [
                    null
                ]
            },
            {
                "dp_amount": 800000,
                "tenor": [
                    "35"
                ],
                "installment_amount": 1039000,
                "finco_name": "PT OTO FINANCE",
                "finco_branch": "",
                "installment_amount_strikethrough": 0,
                "finco_code": "OTOF",
                "otr": 0,
                "tenor_strikethrough": [
                    null
                ]
            }
        ],
        "data_entry_option": [],
        "image_campaign_custom": [],
        "event_points": [
            {
                "name": "melengkapi data pemilik",
                "key": "contact",
                "point": 2
            },
            {
                "name": "melengkapi data pemilik + wa verify",
                "key": "contact_wa_verify",
                "point": 5
            },
            {
                "name": "melengkapi data ktp",
                "key": "id_card",
                "point": 3
            },
            {
                "name": "melengkapi data kk",
                "key": "family_card",
                "point": 3
            },
            {
                "name": "melengkapi image selfie",
                "key": "selfie",
                "point": 3
            },
            {
                "name": "kirim aplikasi",
                "key": "send_application",
                "point": 3
            }
        ],
        "default_finco_id": "6730637160071",
        "default_dealer_code": "",
        "default_dealer_name": "",
        "ideal_conversation_flow": "6730637160071",
        "ads_parameter": [],
        "gross_return_data": 0,
        "source_id": "6730637160071",
        "deal_type": "DEAL"
    }
}
```

