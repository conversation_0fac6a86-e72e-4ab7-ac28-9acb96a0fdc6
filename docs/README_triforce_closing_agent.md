# Triforce Closing Agent

## Overview
Triforce Closing Agent adalah AI agent yang di<PERSON><PERSON>an untuk percakapan sales closing menggunakan teknik **Triforce** (Trust, Value, Urgency).

## Features

### 🎯 Teknik Triforce Closing
1. **Trust (Kepercayaan)** - Membangun rapport dan kredibilitas
2. **Value (Nilai)** - Menjelaskan manfaat konkret dan ROI  
3. **Urgency (Urgensi)** - Menciptakan sense of urgency yang natural

### 📊 Analisis Prospek
- Mendeteksi tingkat minat prospek (rendah, sedang, tinggi, sangat tinggi)
- Mengidentifikasi tahap dalam proses penjualan
- Mengenali objeksi dan sinyal buying

### 💬 Optimized untuk WhatsApp
- Format pesan maksimal 4096 karakter
- Penggunaan emoji yang tepat
- Bullet points yang mudah dibaca
- Pertanyaan closing yang efektif

## Usage

### Basic Usage
```python
from app.agents.triforce_closing_agent import TriforceClosingAgent
from app.agents.states.triforce_closing_agent_state import TriforceClosingAgentState
from langchain_core.messages import HumanMessage

# Initialize agent
agent = TriforceClosingAgent()

# Create initial state
state = TriforceClosingAgentState(
    messages=[
        HumanMessage(content="Saya tertarik dengan produk Anda")
    ],
    thread_id="prospect_001",
    user_name="John Doe"
)

# Get response
result = await agent.runnable.ainvoke(state)
print(result['last_response'])
```

### State Properties
- `prospect_interest_level`: Level minat prospek
- `closing_stage`: Tahap dalam proses closing
- `objections_raised`: Daftar objeksi yang diangkat
- `deal_value`: Nilai deal yang dibahas
- `next_action`: Aksi selanjutnya yang disarankan

## Teknik Closing yang Digunakan

1. **Assumptive Close**: "Kapan kita bisa mulai implementasi?"
2. **Alternative Close**: "Lebih prefer paket A atau B?"
3. **Urgency Close**: "Promo ini berakhir hari ini"
4. **Trial Close**: "Bagaimana menurut Anda sejauh ini?"
5. **Summary Close**: "Jadi benefit utamanya adalah..."

## Handling Objections

- **"Harga terlalu mahal"** → Fokus pada ROI dan value
- **"Perlu pikir dulu"** → Bantu identifikasi keraguan spesifik
- **"Belum ada budget"** → Tunjukkan cost of inaction
- **"Perlu diskusi tim"** → Tawarkan presentasi untuk tim

## Testing

Jalankan file example untuk testing:
```bash
python app/agents/triforce_closing_agent_example.py
```

## Architecture

Agent ini menggunakan LangGraph dengan 3 node utama:
1. **analyze_prospect**: Analisis tingkat minat dan tahap prospek
2. **closing_strategy**: Generate strategi closing yang sesuai
3. **finalize**: Finalisasi state conversation

## Dependencies

- LangGraph
- LangChain Core
- Gemini LLM (via app.llm.gemini)

## Notes

Ini adalah versi basic dari Triforce Closing Agent. Fitur yang bisa ditambahkan:
- Structured output untuk analisis yang lebih akurat
- Integration dengan CRM
- A/B testing untuk berbagai teknik closing
- Analytics dan reporting
- Multi-language support
