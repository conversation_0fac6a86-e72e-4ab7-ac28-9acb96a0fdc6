# Planning Tool

## Deskripsi

Tool `planning` adalah alat strategis yang digunakan untuk merekam rencana jangka pendek, melacak kemajuan eks<PERSON><PERSON>i, mengoordinasikan penggunaan alat lain, dan memberikan pembaruan status dalam sistem AI agent. Tool ini berfungsi sebagai pusat koordinasi untuk perencanaan dan eksekusi tugas yang kompleks.

## Fungsi Utama

- Merekam dan mengelola rencana jangka pendek
- Melacak kemajuan eksekusi tugas
- Mengoordinasikan penggunaan tools lainnya
- Memberikan pembaruan status real-time
- Memfasilitasi decision making dalam workflow

## Parameter Input

### Parameter Wajib

#### `intent` (string)
Tujuan atau maksud dari perencanaan yang akan dilakukan.

```python
"intent": "Menganalisis data penjualan dan memberikan rekomendasi"
```

#### `plans` (array of strings)
Daftar rencana atau langkah-langkah yang akan dieksekusi.

```python
"plans": [
    "Mengumpulkan data penjualan dari database",
    "Menganalisis tren penjualan bulanan",
    "Membuat visualisasi data",
    "Menyusun rekomendasi berdasarkan analisis"
]
```

### Parameter Opsional

#### `plan_now` (string, optional)
Rencana yang sedang dieksekusi saat ini.

```python
"plan_now": "Mengumpulkan data penjualan dari database"
```

#### `finished_plans` (array of strings, optional)
Daftar rencana yang telah selesai dieksekusi.

```python
"finished_plans": [
    "Mengumpulkan data penjualan dari database",
    "Menganalisis tren penjualan bulanan"
]
```

#### `tool_choice` (string, optional)
Tool yang dipilih untuk eksekusi rencana saat ini.

```python
"tool_choice": "get_sales_data_tool"
```

## Output

Tool ini mengembalikan konfirmasi perencanaan yang telah direkam:

```
"Planning recorded successfully with intent: [intent] and [number] plans."
```

Contoh output:
```
"Planning recorded successfully with intent: Menganalisis data penjualan dan memberikan rekomendasi and 4 plans."
```

## Implementasi

### Fungsi Utama

```python
async def planning_tool(
    intent: str,
    plans: List[str],
    plan_now: Optional[str] = None,
    finished_plans: Optional[List[str]] = None,
    tool_choice: Optional[str] = None
) -> str:
    """
    Merekam rencana jangka pendek dan melacak kemajuan.
    
    Args:
        intent: Tujuan atau maksud dari perencanaan
        plans: Daftar rencana yang akan dieksekusi
        plan_now: Rencana yang sedang dieksekusi (opsional)
        finished_plans: Daftar rencana yang telah selesai (opsional)
        tool_choice: Tool yang dipilih untuk eksekusi (opsional)
    
    Returns:
        str: Konfirmasi bahwa perencanaan telah direkam
    """
    # Implementasi logging dan state management
    return f"Planning recorded successfully with intent: {intent} and {len(plans)} plans."
```

### Karakteristik Tool

1. **Flexible Parameters**: Kombinasi parameter wajib dan opsional
2. **State Tracking**: Melacak status eksekusi rencana
3. **Tool Coordination**: Mengkoordinasikan penggunaan tools lainnya
4. **Progress Monitoring**: Memantau kemajuan eksekusi

## Kapan Menggunakan Tool Ini

Tool ini digunakan dalam situasi berikut:

1. **Perencanaan Kompleks**: Ketika tugas memerlukan multiple steps
2. **Koordinasi Tools**: Untuk mengatur urutan penggunaan tools
3. **Progress Tracking**: Melacak kemajuan tugas yang sedang berjalan
4. **Decision Making**: Sebagai basis untuk pengambilan keputusan
5. **Workflow Management**: Mengelola alur kerja yang kompleks

## Contoh Penggunaan

### Contoh 1: Perencanaan Analisis Data

```python
from app.tools.planning_tool import planning_tool

# Perencanaan awal
result = await planning_tool.ainvoke({
    "intent": "Menganalisis performa penjualan Q4",
    "plans": [
        "Mengumpulkan data penjualan Q4",
        "Membandingkan dengan Q3",
        "Mengidentifikasi tren",
        "Membuat laporan rekomendasi"
    ]
})
```

### Contoh 2: Update Progress

```python
# Update ketika sedang mengeksekusi rencana
result = await planning_tool.ainvoke({
    "intent": "Menganalisis performa penjualan Q4",
    "plans": [
        "Mengumpulkan data penjualan Q4",
        "Membandingkan dengan Q3",
        "Mengidentifikasi tren",
        "Membuat laporan rekomendasi"
    ],
    "plan_now": "Membandingkan dengan Q3",
    "finished_plans": ["Mengumpulkan data penjualan Q4"],
    "tool_choice": "get_sales_comparison_tool"
})
```

### Contoh 3: Perencanaan Customer Service

```python
# Perencanaan untuk menangani inquiry customer
result = await planning_tool.ainvoke({
    "intent": "Menangani inquiry tentang promo kendaraan",
    "plans": [
        "Mengidentifikasi jenis inquiry",
        "Mencari informasi promo yang relevan",
        "Menyiapkan informasi dealer terdekat",
        "Memberikan respons komprehensif"
    ],
    "plan_now": "Mengidentifikasi jenis inquiry"
})
```

## Integrasi dengan Sistem

### State Management

Tool ini terintegrasi dengan `AgentBasicState` untuk:
- Menyimpan rencana dalam state
- Melacak progress eksekusi
- Mengkoordinasikan dengan tools lainnya
- Memfasilitasi decision making

### Workflow Integration

Dalam alur kerja agent:
1. Agent menerima tugas kompleks
2. Agent menggunakan planning tool untuk membuat rencana
3. Agent mengeksekusi rencana step by step
4. Agent mengupdate progress menggunakan planning tool
5. Agent menyelesaikan tugas dan memberikan hasil

## Error Handling

### Validasi Input

```python
# Validasi parameter wajib
if not intent or not plans:
    raise ValueError("Intent and plans are required parameters")

# Validasi format plans
if not isinstance(plans, list) or len(plans) == 0:
    raise ValueError("Plans must be a non-empty list")

# Validasi konsistensi finished_plans
if finished_plans and plan_now:
    if plan_now in finished_plans:
        raise ValueError("Current plan cannot be in finished plans")
```

### Error Scenarios

1. **Missing Required Parameters**: Intent atau plans tidak disediakan
2. **Invalid Plan Format**: Plans bukan list atau kosong
3. **Inconsistent State**: plan_now ada dalam finished_plans
4. **Tool Choice Error**: tool_choice tidak valid atau tidak tersedia

## Best Practices

### 1. Perencanaan yang Jelas

```python
# GOOD: Rencana yang spesifik dan actionable
"plans": [
    "Mengambil data dealer untuk organisasi Toyota",
    "Memformat informasi kontak dan alamat",
    "Menyiapkan informasi jam operasional",
    "Mengembalikan respons terstruktur"
]

# BAD: Rencana yang terlalu umum
"plans": [
    "Cari data",
    "Proses data",
    "Kirim hasil"
]
```

### 2. Progress Tracking yang Konsisten

```python
# Selalu update finished_plans ketika menyelesaikan step
finished_plans.append(completed_plan)
plan_now = next_plan
```

### 3. Tool Coordination

```python
# Spesifikasi tool yang akan digunakan untuk setiap step
tool_mapping = {
    "Mengambil data dealer": "get_dealer_data_tool",
    "Mengambil info promo": "get_vehicle_promo_tool",
    "Simulasi kredit": "get_credit_simulation_tool"
}
```

## Advanced Usage

### Conditional Planning

```python
# Perencanaan dengan kondisi
if user_needs_dealer_info:
    plans.append("Mencari informasi dealer terdekat")
if user_needs_promo:
    plans.append("Mencari promo yang tersedia")
```

### Dynamic Plan Updates

```python
# Update rencana berdasarkan hasil eksekusi
if analysis_result.needs_additional_data:
    plans.insert(current_index + 1, "Mengumpulkan data tambahan")
```

### Parallel Execution Planning

```python
# Perencanaan untuk eksekusi paralel
parallel_plans = [
    "Mengambil data dealer (parallel)",
    "Mengambil data promo (parallel)",
    "Menggabungkan hasil"
]
```

## Monitoring dan Analytics

### Metrics yang Dapat Ditrack

1. **Plan Completion Rate**: Persentase rencana yang berhasil diselesaikan
2. **Average Execution Time**: Waktu rata-rata eksekusi per plan
3. **Tool Usage Frequency**: Frekuensi penggunaan tools dalam planning
4. **Plan Complexity**: Jumlah rata-rata steps per intent

### Logging

```python
import logging

logger = logging.getLogger(__name__)

async def planning_tool(intent: str, plans: List[str], **kwargs) -> str:
    logger.info(f"Planning initiated with intent: {intent}")
    logger.info(f"Number of plans: {len(plans)}")
    
    # Log current progress if available
    if kwargs.get('finished_plans'):
        progress = len(kwargs['finished_plans']) / len(plans) * 100
        logger.info(f"Current progress: {progress:.1f}%")
    
    return f"Planning recorded successfully with intent: {intent} and {len(plans)} plans."
```

## Testing

### Unit Tests

```python
import pytest
from app.tools.planning_tool import planning_tool

@pytest.mark.asyncio
async def test_basic_planning():
    """Test basic planning functionality."""
    result = await planning_tool.ainvoke({
        "intent": "Test planning",
        "plans": ["Step 1", "Step 2", "Step 3"]
    })
    
    assert "Planning recorded successfully" in result
    assert "Test planning" in result
    assert "3 plans" in result

@pytest.mark.asyncio
async def test_planning_with_progress():
    """Test planning with progress tracking."""
    result = await planning_tool.ainvoke({
        "intent": "Test with progress",
        "plans": ["Step 1", "Step 2", "Step 3"],
        "plan_now": "Step 2",
        "finished_plans": ["Step 1"],
        "tool_choice": "test_tool"
    })
    
    assert "Planning recorded successfully" in result

@pytest.mark.asyncio
async def test_invalid_input():
    """Test error handling for invalid input."""
    with pytest.raises(ValueError):
        await planning_tool.ainvoke({
            "intent": "",
            "plans": []
        })
```

### Integration Tests

```python
@pytest.mark.asyncio
async def test_planning_integration():
    """Test planning tool integration with other tools."""
    # Test planning for dealer data retrieval
    planning_result = await planning_tool.ainvoke({
        "intent": "Mengambil informasi dealer Toyota",
        "plans": [
            "Validasi organisasi",
            "Mengambil data dealer",
            "Format respons"
        ],
        "plan_now": "Validasi organisasi",
        "tool_choice": "get_dealer_data_tool"
    })
    
    assert "Planning recorded successfully" in planning_result
    
    # Test actual tool execution
    from app.tools.get_dealer_data_tool import get_dealer_data
    dealer_result = await get_dealer_data.ainvoke({"organization": "toyota"})
    
    assert dealer_result is not None
```

## Relasi dengan Tools Lainnya

### Koordinasi dengan Get Dealer Data Tool

```python
# Planning untuk mengambil data dealer
planning_result = await planning_tool.ainvoke({
    "intent": "Menyediakan informasi dealer lengkap",
    "plans": [
        "Validasi parameter organisasi",
        "Mengambil data dealer dari service",
        "Format informasi kontak dan alamat",
        "Menyiapkan informasi jam operasional",
        "Mengembalikan respons terstruktur"
    ],
    "tool_choice": "get_dealer_data_tool"
})
```

### Koordinasi dengan AI Final Answer Tool

```python
# Planning yang berakhir dengan final answer
planning_result = await planning_tool.ainvoke({
    "intent": "Memberikan jawaban komprehensif tentang promo",
    "plans": [
        "Menganalisis kebutuhan customer",
        "Mencari promo yang relevan",
        "Menyiapkan informasi dealer",
        "Menyusun jawaban final"
    ],
    "finished_plans": [
        "Menganalisis kebutuhan customer",
        "Mencari promo yang relevan",
        "Menyiapkan informasi dealer"
    ],
    "plan_now": "Menyusun jawaban final",
    "tool_choice": "ai_final_answer_tool"
})
```

## Catatan Pengembangan

1. **Scalability**: Tool dirancang untuk menangani rencana dengan kompleksitas berbeda
2. **Flexibility**: Parameter opsional memungkinkan adaptasi dengan berbagai use case
3. **Integration**: Mudah diintegrasikan dengan tools dan systems lainnya
4. **Monitoring**: Built-in support untuk tracking dan analytics
5. **Error Handling**: Robust error handling untuk berbagai skenario

## Roadmap

### Fitur yang Direncanakan

1. **Plan Templates**: Template rencana untuk use case umum
2. **Conditional Execution**: Eksekusi rencana berdasarkan kondisi
3. **Parallel Processing**: Support untuk eksekusi rencana paralel
4. **Plan Optimization**: Optimasi urutan eksekusi berdasarkan dependencies
5. **Visual Planning**: Interface visual untuk perencanaan kompleks

### Improvements

1. **Performance**: Optimasi untuk rencana dengan banyak steps
2. **Memory Management**: Efficient memory usage untuk long-running plans
3. **Persistence**: Penyimpanan rencana untuk recovery
4. **Analytics**: Advanced analytics untuk plan optimization

## Changelog

- **v1.0**: Implementasi awal dengan basic planning functionality
- **v1.1**: Penambahan progress tracking dan tool coordination
- **v1.2**: Implementasi error handling dan validasi
- **v1.3**: Peningkatan logging dan monitoring capabilities
- **v1.4**: Optimasi performance dan memory usage