# Credit Simulation Service

## Deskripsi

Service `CreditSimulationService` bertanggung jawab untuk mengambil data simulasi kredit dari API B2B. Service ini memungkinkan sistem untuk mendapatkan perhitungan cicilan berdasarkan model kendaraan, kode varian, dan grup kota (area) yang spesifik.

Data yang dihasilkan oleh service ini sangat penting untuk menjawab pertanyaan pengguna mengenai kemungkinan skema kredit, se<PERSON>i jumlah D<PERSON>, tenor, dan angs<PERSON>n bulanan.

## Komponen

Service ini didefinisikan dalam `app/services/credit_simulation_service.py` dan memiliki struktur sebagai berikut:

-   **Class `CreditSimulationService`**:
    -   Mengelola logika untuk berinteraksi dengan endpoint simulasi kredit.
    -   Mengatur parameter default seperti `leasingCode` yang bernilai `_GLOBAL_`.

-   **Method `aget_credit_simulation(model_name, variant_code, city_group)`**:
    -   **Tujuan**: Mengambil data simulasi kredit yang relevan.
    -   **Argumen**:
        -   `model_name` (string): Nama model kendaraan (contoh: "Vario 125").
        -   `variant_code` (string): Kode unik untuk varian spesifik (contoh: "L1F02N36S4A/T"). Didapatkan dari `get_variants_tool`.
        -   `city_group` (string): Grup kota untuk penyesuaian harga dan leasing (contoh: "cirebon").
    -   **Endpoint**: `/price-list`

## Alur Kerja

1.  Komponen lain, biasanya `get_credit_simulation_tool`, memanggil metode `aget_credit_simulation` dengan parameter yang diperlukan.
2.  Service membuat salinan parameter default dan menambahkan parameter spesifik dari argumen ( `modelName`, `variantCode`, `cityGroup`).
3.  Service mengirimkan permintaan `GET` ke endpoint `/price-list` menggunakan `b2b_api_client`.
4.  Jika permintaan gagal (misalnya, status code bukan 2xx), sebuah `HTTPStatusError` akan dimunculkan untuk menandakan adanya masalah.
5.  Jika berhasil, data JSON yang berisi tabel simulasi kredit akan dikembalikan ke pemanggil.

## Contoh Penggunaan

Kode di bawah ini menunjukkan cara menginisialisasi dan menggunakan `CreditSimulationService` untuk mendapatkan data simulasi kredit.

```python
import asyncio
from app.services.credit_simulation_service import CreditSimulationService

async def main():
    # Inisialisasi service
    service = CreditSimulationService()

    try:
        # Contoh pemanggilan untuk mendapatkan simulasi kredit
        simulation = await service.aget_credit_simulation(
            model_name="Vario 125",
            variant_code="L1F02N36S4A/T",
            city_group="cirebon",
        )
        # Mencetak hasil simulasi
        print(simulation)

    except Exception as e:
        print(f"Terjadi kesalahan: {e}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Endpoint API

Service ini berinteraksi dengan endpoint API berikut:

-   **`GET /price-list`**
    -   **Tujuan**: Mendapatkan daftar harga atau simulasi kredit.
    -   **Parameter Query**:
        -   `leasingCode`: Kode perusahaan leasing (default: `_GLOBAL_`).
        -   `modelName`: Nama model kendaraan.
        -   `variantCode`: Kode varian spesifik.
        -   `cityGroup`: Grup kota target.

## Contoh Respon JSON

Berikut adalah contoh struktur data JSON yang dikembalikan oleh API:

```json
{
    "tabular_view": [
        [
            "Uang Muka",
            "Tenor",
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        ],
        [
            null,
            11,
            17,
            23,
            29,
            35,
            47,
            59,
            null,
            null,
            null,
            null
        ],
        [
            3700000,
            3836000,
            2696000,
            2154000,
            1852000,
            1672000,
            1438000,
            1309000,
            null,
            null,
            null,
            null
        ]
        // ... data rows lainnya
    ],
    "formatted": [
        {
            "dp": 3700000,
            "tenor": 11,
            "installment": 3836000
        },
        {
            "dp": 3700000,
            "tenor": 17,
            "installment": 2696000
        },
        {
            "dp": 3700000,
            "tenor": 23,
            "installment": 2154000
        }
        // ... opsi kredit lainnya
    ],
    "last_updated": "2025-07-29T10:40:56.295Z",
    "area": "BANDUNG",
    "variantCode": "N1N02Q43L1A/T",
    "leasingCode": "_GLOBAL_"
}
```

### Struktur Respons

Respons JSON memiliki format yang terdiri dari beberapa komponen utama:

1. **`tabular_view`**: Array 2D yang menyajikan data dalam format tabel
   - Baris pertama berisi header kolom ("Uang Muka", "Tenor", dll.)
   - Baris kedua berisi nilai-nilai tenor yang tersedia (11, 17, 23, 29, 35, 47, 59 bulan)
   - Baris-baris berikutnya berisi data uang muka dan cicilan bulanan yang sesuai

2. **`formatted`**: Array objek yang menyajikan data dalam format yang lebih mudah digunakan
   - Setiap objek berisi:
     - `dp`: Jumlah uang muka (down payment) dalam Rupiah
     - `tenor`: Jangka waktu kredit dalam bulan
     - `installment`: Jumlah cicilan bulanan dalam Rupiah

3. **Metadata**:
   - `last_updated`: Timestamp terakhir kali data diperbarui
   - `area`: Area atau kota yang menjadi basis kalkulasi (contoh: "BANDUNG")
   - `variantCode`: Kode varian kendaraan yang diminta
   - `leasingCode`: Kode perusahaan leasing (default: "_GLOBAL_")

### Penggunaan Data

Data `formatted` lebih cocok digunakan untuk:
- Pencarian opsi kredit berdasarkan kriteria tertentu
- Presentasi data kepada pengguna
- Perhitungan dan analisis kredit

Data `tabular_view` lebih cocok untuk:
- Menampilkan data dalam format tabel
- Export ke spreadsheet
- Visualisasi data dalam bentuk grid
