# Get Dealer Data Tool

## Deskripsi

Tool `get_dealer_data` digunakan untuk mengambil informasi dealer berdasarkan organisasi yang dikonfigurasi. Tool ini menyediakan detail lengkap tentang dealer termasuk informasi kontak, alamat, jam operasional, media sosial, dan koordinat Google Maps.

## Fungsi Utama

- Mengambil data dealer berdasarkan organisasi yang valid
- Memformat output dalam bentuk yang mudah dibaca
- Menangani berbagai struktur data dealer (array alamat, dealer tunggal, atau array dealer)
- Menyediakan informasi lengkap termasuk koordinat Google Maps

## Cara Kerja

Tool ini tidak memerlukan parameter input dari user atau LLM. Tool secara otomatis mengambil informasi organisasi dari konfigurasi sistem (RunnableConfig) yang sudah diatur sebelumnya. Organisasi yang didukung saat ini adalah "vinfast" dan "amarta" dengan default "amarta".

## Output

Tool ini mengembalikan informasi dealer dalam format terstruktur yang mencakup:

### Informasi Dasar
- **<PERSON>a Dealer**: Nama lengkap dealer
- **Alamat**: Alamat lengkap dealer
- **Kota**: Kota lokasi dealer
- **Provinsi**: Provinsi lokasi dealer
- **Kode Pos**: Kode pos area dealer

### Informasi Kontak
- **Nomor Telepon**: Nomor telepon dealer
- **WhatsApp**: Nomor WhatsApp untuk kontak
- **Email**: Alamat email dealer (jika tersedia)

### Jam Operasional
- **Hari Kerja**: Jam buka dan tutup untuk hari kerja
- **Akhir Pekan**: Jam operasional khusus weekend (jika berbeda)

### Media Sosial
- **Facebook**: Link profil Facebook dealer
- **Instagram**: Link profil Instagram dealer
- **Website**: Website resmi dealer

### Lokasi
- **Google Maps**: Koordinat latitude dan longitude
- **Link Maps**: Link langsung ke Google Maps

## Contoh Penggunaan

### Output
```
=== INFORMASI DEALER AMARTAHONDA ===

Nama: Honda Amarta Bandung
Alamat: Jl. Soekarno Hatta No. 123, Bandung
Kota: Bandung
Provinsi: Jawa Barat
Kode Pos: 40123

KONTAK:
Telepon: (022) 1234567
WhatsApp: 08123456789
Email: <EMAIL>

JAM OPERASIONAL:
Senin - Jumat: 08:00 - 17:00
Sabtu: 08:00 - 15:00
Minggu: Tutup

MEDIA SOSIAL:
Facebook: https://facebook.com/amartahonda
Instagram: https://instagram.com/amartahonda
Website: https://amartahonda.com

LOKASI:
Google Maps: -6.9175, 107.6191
Link Maps: https://maps.google.com/?q=-6.9175,107.6191
```

## Implementasi

### Fungsi Utama

```python
async def _aget_dealer_data_tool(config: RunnableConfig = None) -> str:
    """
    Mengambil data dealer berdasarkan organisasi yang dikonfigurasi.
    
    Args:
        config: RunnableConfig yang berisi informasi organisasi
        
    Returns:
        str: Informasi dealer dalam format yang mudah dibaca
        
    Raises:
        ToolException: Jika organisasi tidak valid atau API call gagal
    """
```

### Validasi Organisasi

Tool melakukan validasi terhadap organisasi yang diambil dari config:
- Memastikan organisasi adalah "vinfast" atau "amarta"
- Menggunakan "amarta" sebagai default jika tidak dikonfigurasi
- Penanganan error untuk organisasi yang tidak valid

### Pemrosesan Data

1. **Inisialisasi Service**: Menggunakan `DealerService` untuk mengambil data
2. **Pengambilan Data**: Memanggil API untuk mendapatkan informasi dealer
3. **Pemformatan Output**: Menggunakan `_format_dealer_data_output` untuk format yang konsisten
4. **Penanganan Error**: Menangani berbagai jenis error dengan pesan yang informatif

### Struktur Data yang Didukung

Tool dapat menangani berbagai struktur data dealer:

1. **Array Alamat**: Dealer dengan multiple alamat
2. **Dealer Tunggal**: Satu dealer dengan satu alamat
3. **Array Dealer**: Multiple dealer dalam satu organisasi

## Error Handling

### Jenis Error yang Ditangani

1. **Organisasi Tidak Valid**
   - Pesan: "Organisasi tidak valid atau tidak ditemukan"
   - Solusi: Periksa nama organisasi yang benar

2. **Data Tidak Ditemukan**
   - Pesan: "Data dealer tidak ditemukan untuk organisasi [nama]"
   - Solusi: Pastikan organisasi memiliki data dealer

3. **Error Koneksi**
   - Pesan: "Terjadi kesalahan saat mengambil data dealer"
   - Solusi: Periksa koneksi internet dan coba lagi

4. **Error Parsing Data**
   - Pesan: "Format data dealer tidak valid"
   - Solusi: Hubungi administrator sistem

## Integrasi dengan Sistem

### Dependencies

- `DealerService`: Service untuk mengambil data dealer dari API
- `StructuredTool`: Framework LangChain untuk tool definition
- `ToolException`: Exception handling khusus untuk tools

### Penggunaan dalam Agent

Tool ini terintegrasi dengan AI agent dan dapat dipanggil ketika:
- User menanyakan informasi dealer
- Diperlukan data kontak dealer
- User ingin mengetahui lokasi dealer
- Diperlukan informasi jam operasional

## Testing

Untuk testing tool ini:

```python
if __name__ == "__main__":
    import asyncio
    
    async def test_dealer_data():
        # Test dengan konfigurasi vinfast
        config1 = {"configurable": {"organization": "vinfast"}}
        result = await _aget_dealer_data_tool(config1)
        print("Result untuk vinfast:")
        print(result)
        
        # Test dengan konfigurasi amarta
        config2 = {"configurable": {"organization": "amarta"}}
        result2 = await _aget_dealer_data_tool(config2)
        print("\nResult untuk amarta:")
        print(result2)
    
    asyncio.run(test_dealer_data())
```

## Catatan Pengembangan

1. **Performance**: Tool menggunakan async/await untuk performa optimal
2. **Caching**: Pertimbangkan implementasi caching untuk data dealer yang jarang berubah
3. **Monitoring**: Log semua request untuk monitoring dan debugging
4. **Scalability**: Struktur code mendukung penambahan organisasi baru

## Changelog

- **v1.0**: Implementasi awal dengan dukungan basic dealer data
- **v1.1**: Penambahan support untuk multiple dealer structures
- **v1.2**: Peningkatan error handling dan validasi input
- **v1.3**: Penambahan informasi Google Maps coordinates