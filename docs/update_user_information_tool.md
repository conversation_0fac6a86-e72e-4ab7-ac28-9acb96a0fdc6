# Feature Documentation: Update User Information

This feature intelligently identifies and stores the user's full name and location (domicile). This is a crucial step in providing services relevant to the user's region, such as city-specific OTR (On The Road) prices, as well as personalizing greetings.

## Main Components

1.  **`determine_city_group` (`app/helpers/determine_city_group.py`)**
    -   The core function that translates the user's location input (which can be a city name, district, address, or even a city nickname) into a standardized `city_group` (e.g., "bandung", "jakarta").
    -   Uses an LLM to perform classification and reasoning, enabling it to recognize various location input formats.

2.  **`update_city_group_service` (`app/services/update_city_group_service.py`)**
    -   Responsible for sending the identified `city_group` to an external API to be saved in the user's profile/chat room.

3.  **`update_name_service` (`app/services/update_name_service.py`)**
    -   Responsible for sending the identified user's name to an external API to be saved in the user's profile/chat room.

4.  **`update_user_information_tool` (`app/tools/update_user_information_tool.py`)**
    -   A `StructuredTool` called by the AI agent when the user mentions their name and/or location.
    -   Orchestrates the flow: calls `determine_city_group` for location identification, calls `update_city_group_service` to save the result, and calls `update_name_service` to save the name.

## Workflow

1.  The user mentions their name and/or location in the conversation. Examples: "My name is Budi, I'm from Bandung", "I live in the City of Flowers", "My house is in Cikarang", "I'm from Surabaya", "Call me Andi".
2.  The AI agent detects this intent and calls the `update_user_information_tool` with the mentioned name and/or location as the `real_name` and `city` parameters.
3.  If `city` is mentioned, the tool first calls `determine_city_group`. This helper will ask the LLM to analyze the `city` input and return it in a structured format containing the `area` and `reason` (reason for determining the area).
4.  If the `area` is successfully determined, the tool will call `update_city_group` to save this information to the system via an API.
5.  If `real_name` is mentioned, the tool will call `update_name` to save this information to the system via an API.
6.  The tool returns a `Command` to LangGraph, which will update the conversation state with the new `real_name` and/or `city_group`, so it can be used by other tools (like `get_variants` or `get_credit_simulation`) and for personalizing greetings.

## Usage Example (in code)

```python
import asyncio
from app.tools.update_user_information_tool import update_user_information_tool

async def example():
    # Example of the tool being called by the agent when the user says "My name is Taufan, I'm from Padalarang"
    result = await update_user_information_tool.ainvoke(
        {
            "real_name": "Taufan",
            "city": "Padalarang",
        },
        config={"configurable": {"chatroom_path": "/path/to/chatroom"}}
    )
    # The result will be a Command to update the state
    print(result)

if __name__ == "__main__":
    asyncio.run(example())
```

## Advantages

-   **Flexible**: Able to understand various ways users mention their name and location.
-   **Intelligent**: Uses the power of LLMs for non-rigid geographical reasoning.
-   **Integrated**: The results are directly saved and used in the conversation state for further service personalization.
