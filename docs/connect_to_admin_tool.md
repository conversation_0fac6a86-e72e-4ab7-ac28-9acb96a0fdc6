# Connect to Admin Tool

## Deskripsi

Tool `connect_to_admin` berfungsi untuk mengalihkan percakapan dari AI ke agen manusia (admin/CS) ketika pengguna memintanya. Tool ini akan menonaktifkan balasan dari AI dan memberikan notifikasi yang ramah dan informatif kepada pengguna, dengan pesan yang disesuaikan berdasarkan jam operasional.

## Komponen

Fitur ini terdiri dari beberapa komponen:

1.  **Tool**: `connect_to_admin.py`
    *   Mendefinisikan `StructuredTool` dengan nama `connect_to_admin`.
    *   Menggunakan `ConnectToAdminSchema` untuk validasi input.
    *   Memanggil service `turn_off_ai_reply` untuk menonaktifkan AI di backend.
    *   Mengembalikan `Command` untuk mengupdate state LangGraph.
    *   Menyediakan pesan yang ramah dan informatif berdasarkan jam operasional.

2.  **Service**: `turn_off_ai_reply_service.py`
    *   Menyediakan fungsi `turn_off_ai_reply` (async) dan `turn_off_ai_reply_sync` (sync) untuk berkomunikasi dengan API yang menonaktifkan AI.

3.  **Schema**: `ConnectToAdminSchema`
    *   Model Pydantic yang mendefinisikan input untuk tool.
    *   Tool ini tidak memerlukan parameter input apapun.

4.  **Enhanced Features**:
    *   **Smart Status Detection**: Fungsi `check_admin_status()` mendeteksi jam operasional secara real-time.
    *   **Contextual Messaging**: Pesan response disesuaikan berdasarkan jam operasional.
    *   **User-Friendly Communication**: Menggunakan bahasa yang ramah, emoji, dan format yang mudah dibaca.
    *   **Helpful Tips**: Memberikan saran dan alternatif untuk pengguna di luar jam operasional.

## Alur Kerja

1.  Pengguna mengirim pesan yang mengindikasikan keinginan untuk berbicara dengan admin (misalnya, "Saya mau bicara dengan CS").
2.  LLM akan memanggil tool `connect_to_admin` tanpa parameter apapun.
3.  Tool `connect_to_admin` akan dieksekusi.
4.  Fungsi `_connect_to_admin` di dalam tool akan memanggil `turn_off_ai_reply_service` untuk menonaktifkan AI melalui API.
5.  Tool mengembalikan `Command` yang berisi:
    *   `ToolMessage` dengan pesan konfirmasi untuk pengguna.
    *   Update state: `connected_to_admin` menjadi `True` dan `turn_completed` menjadi `True`.
6.  Agent akan berhenti membalas, dan percakapan diambil alih oleh admin.

## Penggunaan

### Contoh Pemanggilan Tool

```python
from app.tools.connect_to_admin_tool import connect_to_admin_tool

# Contoh pemanggilan tool
result = await connect_to_admin_tool.ainvoke(
    {},  # Tool tidak memerlukan parameter apapun
    config={
        "configurable": {
            "chatroom_path": "/chatrooms/some_chat_id"
        }
    }
)

print(result)
```

### Contoh Pertanyaan Pengguna

Tool ini dirancang untuk menangani permintaan seperti:

*   "Saya ingin bicara dengan manusia"
*   "Hubungkan saya ke customer service"
*   "Saya butuh bantuan manusia"
*   "Tolong sambungkan ke operator"
*   "Bisa bicara dengan CS?"
*   "Saya mau komplain ke manusia"
*   "AI ini tidak membantu, saya mau bicara dengan orang"
*   "Hubungkan ke tim support"
*   "Saya perlu bantuan langsung dari manusia"
*   "Bisa transfer ke agen?"

## Endpoint API

Service ini menggunakan endpoint API berikut:

```
POST /public/toggle-agent-ai-reply
```

Dengan payload:

```json
{
  "chatRoomPath": "/path/to/chat_room",
  "agentAiReply": false,
  "conversationResume": "User requested connection to admin"
}
```

## Jam Operasional Admin

**Jam Operasional:** Senin - Minggu: 07.00 - 20.00 GMT+7

**Response Time:**
- Dalam jam operasional: 1-5 menit
- Di luar jam operasional: Keesokan hari (jam kerja)

## Pesan Response Berdasarkan Jam Operasional

### Dalam Jam Operasional (07.00-20.00 GMT+7)

```
Baik Kak! 😊 Admin sedang online dan siap membantu 👨‍💼

✅ Percakapan berhasil dialihkan ke admin kami!

⏰ Admin biasanya merespon dalam 1-5 menit selama jam operasional.
📞 Tim customer service kami siap membantu Kakak dengan pelayanan terbaik.

Terima kasih atas kesabaran Kakak! 🙏

_Pesan ini dibalas oleh Marta (Asisten Robot)._
```

### Di Luar Jam Operasional

```
Baik Kak! 😊 Permintaan sudah diterima. Admin akan online kembali besok pagi (07.00 GMT+7) 🕐

✅ Percakapan berhasil dialihkan ke admin kami!

🕐 Saat ini di luar jam operasional (07.00-20.00 GMT+7)
📅 Admin akan merespon pada jam kerja berikutnya (paling lambat keesokan harinya)

💡 Tips untuk Kakak:
• Silakan jelaskan kebutuhan secara detail agar admin dapat membantu optimal
• Untuk informasi produk, Kakak bisa kunjungi website resmi kami
• Tim kami berkomitmen memberikan pelayanan terbaik untuk Kakak

Terima kasih atas pengertian dan kesabaran Kakak! 🙏

_Pesan ini dibalas oleh Marta (Asisten Robot)._
```

## Catatan Implementasi

*   Tool ini harus dipanggil ketika ada indikasi yang jelas dari pengguna untuk berbicara dengan manusia.
*   Tool ini tidak memerlukan parameter input apapun, sehingga mudah digunakan.
*   Flag `connected_to_admin` di state memastikan bahwa agent tidak akan memberikan balasan lebih lanjut setelah tool ini dieksekusi.
*   Sistem akan otomatis menggunakan pesan default "User requested connection to admin" sebagai conversation resume.
*   Pesan response disesuaikan secara otomatis berdasarkan jam operasional untuk memberikan pengalaman pengguna yang lebih baik.
*   Fungsi `check_admin_status()` memberikan informasi real-time tentang status admin dan estimasi waktu online.
