# Trimitra Agent AI - Honda Motorcycle Dealer Chatbot

A FastAPI-based intelligent chatbot application designed specifically for Honda motorcycle dealerships. The chatbot uses a powerful AI agent built with LangGraph, capable of using tools to provide expert assistance and information about Honda motorcycles, parts, services, and dealership operations.

## 📚 Daftar Isi

- [Fitur & Kemampuan](#-fitur--kemampuan)
- [Tumpukan Teknologi](#-tumpukan-teknologi)
- [Struktur Proyek](#-struktur-proyek)
- [Panduan Memulai <PERSON>](#-panduan-memulai-cepat)
- [Dokumentasi API](#-dokumentasi-api)
- [Konfigurasi](#-konfigurasi)
- [Pengembangan](#-pengembangan)
- [<PERSON><PERSON><PERSON>](#-contoh-penggunaan)
- [Deployment](#-deployment)
- [Ko<PERSON><PERSON><PERSON>i](#-kontribusi)
- [Lisensi](#-lisensi)
- [Dukungan](#-dukungan)

## 🏍️ Fitur & Kemampuan

-   **Agen AI Tingkat Lanjut**: Dibangun dengan LangGraph dan komponen siap pakai untuk AI percakapan yang kuat dan stateful dengan kemampuan penggunaan alat yang komprehensif.
-   **Dukungan Multi-LLM**: Terintegrasi dengan Groq (Qwen3-32B) untuk respons berkecepatan tinggi dan Google Gemini untuk kemampuan generasi yang ditingkatkan.
-   **Keahlian Khusus Honda**: Agen AI dilatih dengan pengetahuan khusus domain untuk sepeda motor Honda dan operasi dealer.
-   **Berbasis Alat (Tool-Enabled)**: Menerapkan alat khusus untuk:
    -   `get_variants_tool`: Mengambil informasi varian terperinci termasuk harga, spesifikasi, dan tautan produk untuk model motor Honda.
    -   `get_credit_simulation_tool_and_promo`: Menghitung simulasi kredit dengan parameter yang dapat disesuaikan (DP, tenor, cicilan) dan mengambil data promo.
    -   `update_user_information_tool`: Secara cerdas memproses dan menyimpan data lokasi dan nama pengguna untuk layanan regional.
    -   `connect_to_admin_tool`: Memungkinkan pengguna untuk dengan mulus mengeskalasi percakapan ke agen manusia.
    -   `get_ad_information_tool`: Mengambil informasi iklan berdasarkan ID iklan atau kode promo.
    -   `get_vehicle_promo_tool`: Mendapatkan informasi promo kendaraan berdasarkan model dan varian.
    -   `get_dealer_data_tool`: Mengambil informasi dealer berdasarkan organisasi yang dikonfigurasi.
    -   `planning_tool`: Alat untuk merencanakan eksekusi beberapa alat dan mengelola workflow kompleks.
    -   `agent_turn_complete_tool`: Alat untuk menandai bahwa giliran agen telah selesai.
    -   `ai_final_answer_tool`: Alat untuk memberikan jawaban akhir dari AI.
-   **Backend FastAPI**: Kerangka kerja web Python modern berkinerja tinggi.
-   **API RESTful**: Endpoint HTTP yang bersih untuk integrasi yang mudah.
-   **Respons Terstruktur**: Format respons JSON yang konsisten.
-   **Layanan Berbasis Lokasi**: Memproses lokasi pengguna dengan pemahaman bahasa alami untuk memberikan layanan area regional yang sesuai.
-   **Respons Kontekstual**: Mempertahankan konteks percakapan di beberapa pesan untuk memberikan harga dan ketersediaan khusus wilayah.

## 🛠️ Tumpukan Teknologi

-   **Framework**: FastAPI
-   **AI/ML**:
    - LangChain
    - LangGraph
    - Groq
    - Google Gemini
-   **Backend**:
    - Python
    - Firebase Admin SDK
    - RESTful HTTP endpoints
    - Uvicorn ASGI server
    - Pydantic

## 📁 Struktur Proyek

```
trimitra_agent_ai/
├── .env.example
├── .gitignore
├── Dockerfile
├── README.md
├── requirements.txt
├── test_chat_ideal.http
├── test_summary_endpoint.http
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── agents/
│   │   ├── whatsapp_basic_agent_chat.py
│   │   ├── prompts/
│   │   │   ├── amartamobil/
│   │   │   │   └── whatsapp_basic_agent_prompts_v1.py
│   │   │   └── amartamotor/
│   │   │       ├── whatsapp_basic_agent_prompts_v2.py
│   │   │       └── whatsapp_basic_agent_prompts_v3.py
│   │   └── states/
│   │       └── agent_basic_state.py
│   ├── checkpoints/
│   │   └── firestore_checkpointer.py
│   ├── handlers/
│   │   ├── ideal_chat_handler.py
│   │   └── summary_handler.py
│   ├── helpers/
│   │   ├── determine_city_group.py
│   │   └── firestore_ai_agent_messages.py
│   ├── keys/
│   │   └── firebase/
│   │       └── ideal-trimitra-2620981b291f.json
│   ├── llm/
│   │   ├── gemini.py
│   │   └── groq.py
│   ├── routers/
│   │   ├── chat_ideal_router.py
│   │   └── summary_router.py
│   ├── schemas/
│   │   ├── base_response.py
│   │   └── organization_enum.py
│   ├── services/
│   │   ├── api_client.py
│   │   ├── city_group_service.py
│   │   ├── credit_simulation_service.py
│   │   ├── firebase_service.py
│   │   ├── get_promo_service.py
│   │   ├── model_service.py
│   │   ├── turn_off_ai_reply_service.py
│   │   ├── update_city_group_service.py
│   │   ├── update_name_service.py
│   │   └── variant_service.py
│   ├── structured_outputs/
│   │   └── area_output.py
│   ├── tools/
│   │   ├── agent_turn_complete_tool.py
│   │   ├── ai_final_answer_tool.py
│   │   ├── connect_to_admin_tool.py
│   │   ├── get_ad_information_tool.py
│   │   ├── get_credit_simulation_tool_and_promo.py
│   │   ├── get_dealer_data_tool.py
│   │   ├── get_variants_tool.py
│   │   ├── get_vehicle_promo_tool.py
│   │   ├── planning_tool.py
│   │   └── update_user_information_tool.py
│   └── utils/
│       └── plan_execution.py
├── docs/
│   ├── agent_turn_complete_tool.md
│   ├── ai_final_answer_tool.md
│   ├── connect_to_admin_tool.md
│   ├── credit_simulation_service.md
│   ├── get_ad_information_tool.md
│   ├── get_credit_simulation_tool.md
│   ├── get_dealer_data_tool.md
│   ├── get_promo_service.md
│   ├── get_variants_tool.md
│   ├── get_vehicle_promo_tool.md
│   ├── planning_tool.md
│   ├── update_user_information_tool.md
│   └── plan/
└── test/
    ├── basic_agent_test.py
    ├── basic_agent_test.scenario_2.py
    ├── basic_agent_test.scenario_3.py
    ├── test_determine_city_group_interactive.py
    ├── test_get_variants_tool.py
    ├── test_get_vehicle_promo_tool.py
    ├── test_prebuilt.py
    ├── test_turn_off_ai_reply_service.py
    ├── test_turn_off_ai_reply_tool.py
    ├── turn_off_ai_reply_example.py
    └── checkpoints/
        └── test_firestore_checkpointer.py
```

## 🚀 Panduan Memulai Cepat

### Prerequisites

-   Python 3.11 or higher
-   Groq API key for LLM access
-   Firebase service account credentials
-   Google AI API key (optional, for Gemini)
-   Virtual environment (recommended)

### Installation

1.  **Clone the repository**
    ```bash
    git clone <repository-url>
    cd trimitra_agent_ai
    ```

2.  **Create and activate virtual environment**
    ```bash
    python -m venv .venv
    source .venv/bin/activate  # On Windows: .venv\Scripts\activate
    ```

3.  **Install dependencies**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Set up environment variables**
    ```bash
    cp .env.example .env
    # Edit .env to add:
    #   - GROQ_API_KEY=your_groq_api_key_here
    #   - (optional) GOOGLE_API_KEY=your_google_api_key_here
    
    # Place your Firebase service account JSON key file at:
    # app/keys/firebase/your-service-account-key.json
    ```

5.  **Run the application**
    ```bash
    uvicorn app.main:app --reload
    ```

The API will be available at `http://localhost:8000`. API documentation will be accessible at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 📚 Dokumentasi API

### Chat Endpoint

**POST** `/chat/ideal`

Endpoint untuk berinteraksi dengan chatbot dealer motor Honda.

### Summary Endpoint

**POST** `/summary/ideal`

Endpoint untuk mendapatkan ringkasan percakapan.

### Root Endpoint

**GET** `/`

Endpoint untuk mengecek versi aplikasi.

#### Response
```json
{
  "version": "0.0.1"
}
```

#### Request Body
```json
{
  "messages": [
    {
      "role": "user",
      "text": "Saya mau cek harga motor Beat"
    }
  ]
}
```

#### Response
```json
{
    "status_code": 200,
    "success": true,
    "message": "Request successful",
    "data": {
        "response": "Saya akan bantu cek harga Honda BeAT. Berikut adalah daftar varian BeAT yang tersedia di area Bandung:\n\n1. BeAT CBS\n   Harga: Rp 18.230.000\n   Info: https://amartahonda.com/baru/bandung/H1B02N41L1AT\n\n2. BeAT CBS-ISS\n   Harga: Rp 18.990.000\n   Info: https://amartahonda.com/baru/bandung/H1B02N42L1AT\n\nApakah Anda ingin simulasi kredit untuk varian tertentu?"
    },
    "metadata": {
        "run_id": "a1b2c3d4...",
        "feedback_tokens": [],
        "total_tokens": 800,
        "input_tokens": 500,
        "output_tokens": 300,
        "city_group": "bandung"
    }
}
```

#### Example Usage

```bash
curl -X POST "http://localhost:8000/chat/ideal" \
     -H "Content-Type: application/json" \
     -d '{
       "messages": [
         {
           "role": "user",
           "text": "Tell me about Honda CBR series motorcycles"
         }
       ]
     }'
```

### Interactive API Documentation

Once the server is running, visit:
-   **Swagger UI**: `http://localhost:8000/docs`
-   **ReDoc**: `http://localhost:8000/redoc`

### Dokumentasi Fitur Tambahan

Dokumentasi detail untuk fitur-fitur khusus tersedia di folder `docs/`:

#### Tools Documentation
-   **Agent Turn Complete**: `docs/agent_turn_complete_tool.md`
-   **AI Final Answer**: `docs/ai_final_answer_tool.md`
-   **Connect to Admin**: `docs/connect_to_admin_tool.md`
-   **Get Ad Information**: `docs/get_ad_information_tool.md`
-   **Get Dealer Data**: `docs/get_dealer_data_tool.md`
-   **Get Variants**: `docs/get_variants_tool.md`
-   **Get Vehicle Promo**: `docs/get_vehicle_promo_tool.md`
-   **Planning Tool**: `docs/planning_tool.md`
-   **Update User Information**: `docs/update_user_information_tool.md`

#### Services Documentation
-   **Credit Simulation Service**: `docs/credit_simulation_service.md`
-   **Get Credit Simulation Tool and Promo**: `docs/get_credit_simulation_tool_and_promo.md`
-   **Get Promo Service**: `docs/get_promo_service.md`

## ⚙️ Konfigurasi

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GROQ_API_KEY` | Groq API key for LLM access | Yes |
| `GOOGLE_API_KEY` | Google AI API key for Gemini access | No |

**Note**: Firebase service account key harus ditempatkan di `app/keys/firebase/` directory.

### Model Configuration

The application uses Groq with `qwen/qwen3-32b` as the primary LLM for fast, accurate responses:
-   **Main Model**: Groq (`qwen/qwen3-32b`)
    - Used for all primary interactions
    - Optimized for Indonesian language
    - Temperature: 0.0 for consistent responses
-   **Backup Model**: Google Gemini (optional)
    - Can be enabled as fallback
    - Requires separate API key configuration

## 🧪 Pengembangan

### Running in Development Mode

```bash
# Pastikan virtual environment aktif
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Jalankan server development
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Testing

Proyek ini menyediakan berbagai file test di folder `test/`:

```bash
# Test basic agent
python test/basic_agent_test.py

# Test dengan scenario khusus
python test/basic_agent_test.scenario_2.py
python test/basic_agent_test.scenario_3.py

# Test fitur turn off AI reply
python test/test_turn_off_ai_reply_service.py
python test/test_turn_off_ai_reply_tool.py

# Test interactive city group determination
python test/test_determine_city_group_interactive.py

# Test get variants tool
python test/test_get_variants_tool.py

# Test get vehicle promo tool
python test/test_get_vehicle_promo_tool.py
```

### Project Dependencies

Key dependencies are listed in `requirements.txt`. Major dependencies include:
- FastAPI - Web framework
- LangChain & LangGraph - AI agent framework
- Groq - LLM provider
- Google Generative AI - Alternative LLM provider
- Firebase Admin - Database and authentication

## 📝 Contoh Penggunaan

### Basic Chat Interaction
```python
import requests

response = requests.post(
    "http://localhost:8000/chat/ideal",
    json={
        "messages": [
            {
                "role": "user",
                "text": "What's the difference between Honda CBR1000RR and CBR600RR?"
            }
        ]
    }
)

print(response.json())
```

### Multi-turn Conversation
```python
conversation = [
    {"role": "user", "text": "I'm interested in a Honda motorcycle for commuting"},
    {"role": "assistant", "text": "Great choice! For commuting, I'd recommend..."},
    {"role": "user", "text": "What about fuel efficiency?"}
]

response = requests.post(
    "http://localhost:8000/chat/ideal",
    json={"messages": conversation}
)

print(response.json())
```

## 🚀 Deployment

### Production Deployment

1.  **Set production environment variables**
2.  **Install production dependencies**
3.  **Run with production ASGI server**:
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
   ```

### Docker Deployment

```bash
# Build image
docker build -t trimitra-agent-ai .

# Run container
docker run -d -p 8080:8080 \\
  -e GROQ_API_KEY=your_groq_api_key \\
  -e GOOGLE_API_KEY=your_google_api_key \\
  -v /path/to/firebase/key:/code/app/keys/firebase/key.json \\
  trimitra-agent-ai
```



## 🤝 Kontribusi

1.  Fork the repository
2.  Create a feature branch (`git checkout -b feature/amazing-feature`)
3.  Make your changes
4.  Add tests if applicable
5.  Commit your changes (`git commit -m 'Add some amazing feature'`)
6.  Push to the branch (`git push origin feature/amazing-feature`)
7.  Submit a pull request

## 📄 Lisensi

Proyek ini menggunakan lisensi yang akan ditentukan. Silakan hubungi tim pengembang untuk informasi lebih lanjut.

## 🆘 Dukungan

Untuk dukungan atau pertanyaan tentang chatbot dealer motor Honda ini:
-   Periksa dokumentasi API di `/docs`
-   Tinjau struktur codebase
-   Pastikan konfigurasi environment variable sudah benar
-   Periksa dokumentasi fitur di folder `docs/`
-   Jalankan test untuk memastikan semua komponen berfungsi dengan baik

---

**Built with ❤️ for Honda motorcycle dealerships**